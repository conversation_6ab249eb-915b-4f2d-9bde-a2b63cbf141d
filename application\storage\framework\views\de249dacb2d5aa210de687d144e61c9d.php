<?php
    $data = \DB::table('developement_team')->get();
    $selectedUserIds=[];
    if (!$data->isEmpty()) {
        $users = json_decode($data[0]->user_id);
        $users = App\Models\User::whereIn('id', $users)->get();
        $selectedUserIds = $users->pluck('id')->toArray();
    }

?>
<div class="modal" role="dialog" aria-labelledby="developmentTeamsModal" id="developmentTeamsModal">
    <div class="modal-dialog" id="developmentTeamsModalContainer">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Development Teams</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">
                    <i class="ti-close"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-12">
                        <!-- Add User Form -->
                        <div class="form-group row">
                            <label class="col-sm-12 text-left control-label col-form-label required">Add Team
                                Member</label>
                            <div class="col-sm-12">
                                <select name="team_members" id="team_members"
                                    class="form-control form-control-sm select2-basic select2-multiple select2-hidden-accessible"
                                    multiple="multiple" tabindex="-1" aria-hidden="true">
                                    <?php $__currentLoopData = config('system.team_members'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($user->id); ?>"
                                            <?php echo e(in_array($user->id, $selectedUserIds) ? 'selected' : ''); ?>>
                                            <?php echo e($user->full_name); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-rounded-x btn-secondary waves-effect"
                    data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-rounded-x btn-primary waves-effect"
                    onclick="saveTeamMembers()">Save Changes</button>
            </div>
        </div>
    </div>
</div>

<script src="public/js/dynamic/ajaxDynamic.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
<link href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css" rel="stylesheet" />
<script>
    $(document).ready(function() {
        // Initialize Select2
        $('#team_members').select2({
            placeholder: "Select team members",
            allowClear: true,
            dropdownParent: $('#developmentTeamsModal')
        });
    });

    function removeTeamMember(button) {
        $(button).closest('tr').remove();
    }

    function saveTeamMembers() {
        //add ajax request
        var selectedMembers = $('#team_members').val();
        if (selectedMembers.length === 0) {
            toastr.error('Please select at least one team member.');
            return;
        }
        const url = '/development_teams';


        $.ajax({
            type: 'POST',
            url: url,
            data: {
                _token: '<?php echo e(csrf_token()); ?>',
                selectedMembers: selectedMembers
            },
            success: function(data) {
                if (data == 1) {
                    toastr.success('Team members saved successfully.');
                    $('#developmentTeamsModal').modal('hide');
                }
            },
            error: function(xhr, status, error) {
                toastr.error('Failed to save team members. Please try again later.');
                console.error('Error:', error);
            }
        });
    }
</script>
<?php /**PATH C:\Content\Laravel\CRM-Grow\application\resources\views/pages/team/components/table/development_teamModal.blade.php ENDPATH**/ ?>