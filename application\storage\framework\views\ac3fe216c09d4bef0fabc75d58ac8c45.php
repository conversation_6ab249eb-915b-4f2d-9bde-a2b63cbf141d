<div class="tasks-overview">
    <h3 class="overview-title">Total Tasks Status</h3>
</div>

<div class="task-stats-container">
    <div class="task-stats-row">
        <div class="task-stat-card" data-filter="completed" onclick="toggleFilter(this)">
            <div class="stat-icon">
                <i class="ti-check-box"></i>
            </div>
            <div class="stat-content">
                <a href="/tasks/?filter_tasks_status[]=4&force_view=list&filter_phase_id=<?php echo e(session('filter_phase_id')); ?>&filter_task_projectid=<?php echo e(session('filter_project_id')); ?>">
                    <div class="stat-count"><?php echo e($totalCompletedTasks); ?> Completed</div>
                    <div class="stat-period">till now</div>
                </a>
                
            </div>
        </div>

        <div class="task-stat-card" data-filter="updated" onclick="toggleFilter(this)">
            <div class="stat-icon">
                <i class="ti-reload"></i>
            </div>
            <div class="stat-content">
                <a href="/tasks/?filter_tasks_status[]=1&force_view=list&filter_phase_id=<?php echo e(session('filter_phase_id')); ?>&filter_task_projectid=<?php echo e(session('filter_project_id')); ?>&filter_phase_date_start=<?php echo e(\Carbon\Carbon::now()->format('Y-m-d')); ?>"> 
                    <div class="stat-count"><?php echo e($totalUpcomingTasks); ?> Upcoming </div>
                    <div class="stat-period">next 7 days</div>
                </a>
            </div>
        </div>
        
        <div class="task-stat-card" data-filter="inprogress" onclick="toggleFilter(this)">
            <div class="stat-icon">
                <i class="ti-loop"></i>
            </div>
            <div class="stat-content">
                <a
                    href="/tasks/?filter_tasks_status[]=2&filter_phase_id=<?php echo e(session('filter_phase_id')); ?>&filter_task_projectid=<?php echo e(session('filter_project_id')); ?>">
                    <div class="stat-count"><?php echo e($totalInprogressTasks); ?> Inprogress</div>
                    <div class="stat-period">till now</div>
                </a>

            </div>
        </div>



        <div class="task-stat-card" data-filter="due" onclick="toggleFilter(this)">
            <div class="stat-icon">
                <i class="ti-time"></i>
            </div>
            <div class="stat-content">
                <a href="/tasks/?filter_phase_id=<?php echo e(session('filter_phase_id')); ?>&filter_task_projectid=<?php echo e(session('filter_project_id')); ?>&filter_phase_date_end=<?php echo e(\Carbon\Carbon::now()->format('Y-m-d')); ?>">
                    <div class="stat-count"><?php echo e($totalTaskEndNext7Days); ?> due soon</div>
                    <div class="stat-period">in the next 7 days</div>
                </a>
            </div>
        </div>
    </div>
</div>

<style>
    .task-stats-container {
        margin-bottom: 15px;
    }

    .task-stats-row {
        display: flex;
        gap: 8px;
    }

    .task-stat-card {
        flex: 1 1 calc(50% - 8px);
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
        padding: 10px;
        display: flex;
        align-items: center;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        margin-bottom: 8px;
        position: relative;
        overflow: hidden;
    }

    .task-stat-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 15px rgba(52, 152, 219, 0.2);
    }

    .task-stat-card:hover .stat-icon {
        transform: scale(1.1);
        background: rgba(52, 152, 219, 0.2);
    }

    .task-stat-card:hover .stat-count {
        color: #3498db;
    }

    .task-stat-card:before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 0;
        background: #3498db;
        transition: height 0.3s ease;
    }

    .task-stat-card:hover:before {
        height: 100%;
    }

    .task-stat-card.active {
        border-left: none;
        background-color: #f8f9fa;
    }

    .task-stat-card.active:before {
        height: 100%;
    }

    .stat-icon {
        margin-right: 12px;
        color: #3498db;
        font-size: 1.2rem;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 30px;
        height: 30px;
        background: rgba(52, 152, 219, 0.1);
        border-radius: 50%;
        transition: all 0.3s ease;
    }

    .stat-content {
        display: flex;
        flex-direction: column;
    }

    .stat-count {
        font-weight: 600;
        font-size: 13px;
        color: #2c3e50;
    }

    .stat-period {
        font-size: 11px;
        color: #7f8c8d;
    }

    .overview-title {
        font-size: 18px;
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 5px;
    }
</style>

<script>
    function toggleFilter(element) {
        // Toggle active class
        element.classList.toggle('active');

        // Get all active filters
        const activeFilters = document.querySelectorAll('.task-stat-card.active');
        const activeFilterTypes = Array.from(activeFilters).map(item => item.dataset.filter);

        // Here you would typically trigger your filtering logic
        console.log('Active filters:', activeFilterTypes);
    }
</script>
<?php /**PATH C:\Content\Laravel\CRM-Grow\application\resources\views/pages/task_dashboard/components/modals/task-card.blade.php ENDPATH**/ ?>