<div class="d-flex justify-content-between align-items-center mb-4">
    <h3 class="card-title">Subtasks</h3>
    <?php if($task->permission_edit_task): ?>
        <button type="button" class="btn btn-info btn-sm edit-add-modal-button js-ajax-ux-request" data-toggle="modal"
            data-target="#commonModal" data-url="<?php echo e(url('/tasks/subtasks/create?task_id=' . $task->task_id)); ?>"
            data-loading-target="commonModalBody" data-modal-title="Add Subtasks"
            data-action-url="<?php echo e(url('/tasks/subtasks/create?task_id=' . $task->task_id)); ?>" data-action-method="POST"
            data-action-ajax-loading-target="commonModalBody">
            <i class="ti-plus"></i> Add Subtasks
        </button>
    <?php endif; ?>
</div>

<?php
    $subtasks = $task->subtasks ?? collect([]);
    $taskstatuses=\App\Models\TaskStatus::all();
    // Import str
    use Illuminate\Support\Str;
    // dd($taskstatuses);
?>
<div class="subtasks-wrapper" id="subtasks-container">
    <?php $__currentLoopData = $subtasks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $subtask): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <div class="subtask-card" data-subtask-id="<?php echo e($subtask->id); ?>">
            <div class="subtask-header" onclick="subtaskManager.toggleChecklist(<?php echo e($subtask->id); ?>)">
                <div class="subtask-info">
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-center">
                            <div class="subtask-title me-4" title="<?php echo e($subtask->subtask_title); ?>">
                                <i class="fas fa-tasks me-2"></i>
                                <?php echo e(Str::limit($subtask->subtask_title, 30)); ?>

                            </div>
                            <div class="subtask-dates me-3">
                                <i class="far fa-calendar-alt"></i>
                                <?php echo e(\Carbon\Carbon::parse($subtask->subtask_start_date)->format('M d')); ?> -
                                <?php echo e(\Carbon\Carbon::parse($subtask->subtask_end_date)->format('M d, Y')); ?>

                            </div>
                            <div class="subtask-assignees">
                                <?php if($subtask->assignedUser && $subtask->assignedUser->first_name): ?>
                                    <span class="badge badge-light-primary">
                                        <i class="fas fa-user-circle"></i>
                                        <?php echo e($subtask->assignedUser->first_name); ?>

                                    </span>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="d-flex align-items-center action-group">
                            <div class="subtask-status">
                                <select class="form-control form-control-sm status-select" 
                                        onchange="subtaskManager.updateStatus(<?php echo e($subtask->id); ?>, this.value, this.options[this.selectedIndex].text)"
                                        onclick="event.stopPropagation();">
                                    <?php $__currentLoopData = $taskstatuses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $status): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($status->taskstatus_id); ?>" 
                                                <?php echo e($subtask->subtask_status_id == $status->taskstatus_id ? 'selected' : ''); ?>>
                                            <?php echo e($status->taskstatus_title); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                            <div class="subtask-actions ms-3">
                                <button type="button" 
                                    class="btn btn-icon btn-sm btn-danger-light delete-subtask-btn" 
                                    onclick="event.stopPropagation(); subtaskManager.deleteSubtask(<?php echo e($subtask->id); ?>)">
                                    <i class="sl-icon-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="checklist-container" id="checklist-<?php echo e($subtask->id); ?>">
                <div class="progress-wrapper">
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 0%"></div>
                    </div>
                    <div class="progress-text mt-2">
                        <small>0% Complete</small>
                    </div>
                </div>
                <div class="checklist-items">
                    <!-- Checklist items will be loaded here -->
                </div>
                <form class="add-checklist-form" onsubmit="subtaskManager.addChecklistItem(event, <?php echo e($subtask->id); ?>)"
                    data-no-dynamic="true">
                    <input type="text" class="add-checklist-input" placeholder="Add new checklist item...">
                    <button type="submit" class="add-checklist-btn">
                        <i class="fas fa-plus"></i> Add
                    </button>
                </form>
            </div>
        </div>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
</div>

<script>
    $('#commonModal').on('hidden.bs.modal', function() {
        $('body').removeClass('modal-open');
        $('body').css('overflow', '');
        $('body').css('padding-right', '');
        $('#cardModal').css('overflow-y', 'auto');
        $('.modal-backdrop').remove();
    });

    // Ensure main modal stays scrollable when subtask modal opens
    $('#commonModal').on('show.bs.modal', function() {
        $('#cardModal').css('overflow-y', 'auto');
    });
    class SubtaskManager {
        constructor() {
            this.activeSubtaskId = null;
            this.subtasksData = new Map();
            this.init();
        }

        init() {
            // No need to load subtasks as they're already rendered from PHP
            this.attachEventListeners();
        }

        attachEventListeners() {
            // Add any global event listeners here
        }

        toggleChecklist(subtaskId) {
            const container = document.getElementById(`checklist-${subtaskId}`);
            console.log('Toggle container:', container); // Debug log

            // Close previously open checklist if exists
            if (this.activeSubtaskId && this.activeSubtaskId !== subtaskId) {
                const previousContainer = document.getElementById(`checklist-${this.activeSubtaskId}`);
                if (previousContainer) {
                    previousContainer.style.display = 'none';
                }
            }

            // If current checklist is open, just close it
            if (container.style.display === 'block') {
                container.style.display = 'none';
                this.activeSubtaskId = null;
                return;
            }

            // Always load fresh data when opening
            this.loadChecklistItems(subtaskId)
                .then((data) => {
                    // Show container after data is loaded
                    container.style.display = 'block';
                    this.activeSubtaskId = subtaskId;
                })
                .catch(error => {
                    console.error('Error in toggleChecklist:', error); // Debug log
                    toastr.error('Error loading checklist items');
                });
        }

        async loadChecklistItems(subtaskId) {
            try {
                const response = await fetch(`/subtasks/checklist?subtask_id=${subtaskId}`);
                const data = await response.json();


                const container = document.getElementById(`checklist-${subtaskId}`);
                if (!container) {
                    console.error('Container not found:', `checklist-${subtaskId}`);
                    return;
                }

                const checklistItems = container.querySelector('.checklist-items');
                if (!checklistItems) {
                    console.error('Checklist items container not found');
                    return;
                }

                // Use data.checklists instead of data.checklist
                const items = Array.isArray(data.checklists) ? data.checklists : [];

                checklistItems.innerHTML = this.renderChecklistItems(items);

                this.updateProgress(subtaskId);

                return data;
            } catch (error) {
                console.error('Error loading checklist items:', error);
                throw error;
            }
        }

        renderChecklistItems(checklist) {

            if (!checklist.length) {
                return '<div class="no-items">No checklist items found</div>';
            }

            return checklist.map(item => `
            <div class="checklist-item" data-item-id="${item.id}">
                <div class="d-flex align-items-center w-100">
                    <input type="checkbox" 
                        class="filled-in chk-col-light-blue checklist-checkbox" 
                        id="checklist_${item.id}"
                        ${item.is_completed ? 'checked' : ''}
                        onchange="subtaskManager.toggleChecklistItem(${item.id}, this.checked)">
                    <label for="checklist_${item.id}" 
                        class="checklist-text ${item.is_completed ? 'completed' : ''}">${item.title}</label>
                </div>
                <button type="button" 
                    class="btn btn-icon btn-sm btn-danger-light delete-checklist-btn" 
                    onclick="subtaskManager.deleteChecklistItem(${item.id})">
                    <i class="sl-icon-trash"></i>
                </button>
            </div>
        `).join('');
        }

        async addChecklistItem(event, subtaskId) {
            event.preventDefault();
            event.stopPropagation(); // Stop event from bubbling up

            const form = event.target;
            const input = form.querySelector('input');
            const title = input.value.trim();

            if (!title) return;

            try {
                const response = await fetch('subtasks/checklist', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    },
                    body: JSON.stringify({
                        subtask_id: subtaskId,
                        title
                    })
                });

                const data = await response.json();
                if (data.status === 'success') {
                    const container = document.getElementById(`checklist-${subtaskId}`);
                    const checklistItemHtml = `
                    <div class="checklist-item" data-item-id="${data.checklist.id}">
                        <div class="d-flex align-items-center w-100">
                            <input type="checkbox" 
                                class="filled-in chk-col-light-blue checklist-checkbox" 
                                id="checklist_${data.checklist.id}"
                                onchange="subtaskManager.toggleChecklistItem(${data.checklist.id}, this.checked)">
                            <label for="checklist_${data.checklist.id}" 
                                class="checklist-text">${data.checklist.title}</label>
                        </div>
                        <button type="button" 
                            class="btn btn-icon btn-sm btn-danger-light delete-checklist-btn" 
                            onclick="subtaskManager.deleteChecklistItem(${data.checklist.id})">
                            <i class="sl-icon-trash"></i>
                        </button>
                    </div>
                `;

                    // Find the checklist items container and append the new item
                    const checklistItems = container.querySelector('.checklist-items');
                    if (checklistItems.querySelector('.no-items')) {
                        checklistItems.innerHTML = ''; // Remove "No items" message if present
                    }
                    checklistItems.insertAdjacentHTML('beforeend', checklistItemHtml);

                    // Clear input and update progress
                    input.value = '';
                    this.updateProgress(subtaskId);
                    toastr.success('Item added successfully');
                } else {
                    toastr.error(data.message || 'Failed to add checklist item');
                }
            } catch (error) {
                console.error('Error adding checklist item:', error);
                toastr.error('Error adding checklist item');
            }
        }

        async toggleChecklistItem(itemId, completed) {
            try {
                const response = await fetch(`subtasks/checklist/toggle/?checklist_id=${itemId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    },
                    body: JSON.stringify({
                        completed
                    })
                });

                if (response.ok) {
                    const item = document.querySelector(`[data-item-id="${itemId}"]`);
                    const text = item.querySelector('.checklist-text');
                    text.classList.toggle('completed', completed);
                    this.updateProgress(this.activeSubtaskId);
                }
            } catch (error) {
                toastr.error('Error updating item');
            }
        }

        async deleteChecklistItem(itemId) {
            try {
                const response = await fetch(`/subtasks/checklist/delete/${itemId}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    }
                });

                const data = await response.json();

                if (data.status === true) {
                    // Remove the item from DOM
                    const itemElement = document.querySelector(`[data-item-id="${itemId}"]`);
                    if (itemElement) {
                        itemElement.remove();
                        // Update progress after removal
                        this.updateProgress(this.activeSubtaskId);
                        // Show success message
                        toastr.success('Checklist item deleted successfully');
                    }
                } else {
                    toastr.error('Failed to delete checklist item');
                }
            } catch (error) {
                console.error('Error deleting checklist item:', error);
                toastr.error('Error deleting checklist item');
            }
        }

        updateProgress(subtaskId) {
            const container = document.getElementById(`checklist-${subtaskId}`);
            const items = container.querySelectorAll('.checklist-item');
            const completed = container.querySelectorAll('.checklist-text.completed').length;
            const progress = items.length ? Math.round((completed / items.length) * 100) : 0;

            container.querySelector('.progress-fill').style.width = `${progress}%`;
            container.querySelector('.progress-text small').textContent = `${progress}% Complete`;
        }

        async deleteSubtask(subtaskId) {
            try {
                const response = await fetch(`tasks/subtasks/delete?subtask_id=${subtaskId}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    }
                });

                const data = await response.json();

                if (data.status === true) {
                    // Remove the subtask card from DOM
                    const subtaskElement = document.querySelector(`[data-subtask-id="${subtaskId}"]`);
                    if (subtaskElement) {
                        subtaskElement.remove();
                        toastr.success('Subtask deleted successfully');
                    }
                } else {
                    toastr.error('Failed to delete subtask');
                }
            } catch (error) {
                console.error('Error deleting subtask:', error);
                toastr.error('Error deleting subtask');
            }
        }

        async updateStatus(subtaskId, statusId, statusTitle) {
            try {
                console.log('Updating status:', { subtaskId, statusId, statusTitle }); // Debug log

                const response = await fetch('subtasks/update-status', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        subtask_id: subtaskId,
                        status_id: statusId
                    })
                });

                const data = await response.json();
                console.log('Response:', data); // Debug log
                
                if (data.status === 'success') {
                    // Find the status select element
                    const statusSelect = document.querySelector(`.subtask-card[data-subtask-id="${subtaskId}"] .status-select`);
                    
                    // Update the selected value
                    statusSelect.value = statusId;
                    
                    toastr.success('Status updated successfully');
                } else {
                    toastr.error('Failed to update status');
                }
            } catch (error) {
                console.error('Error updating status:', error);
                toastr.error('Error updating status');
            }
        }

        // Helper method to get status color
        getStatusColor(statusId) {
            const statusColors = {
                1: 'default',      // New
                2: 'success',      // Completed
                3: 'info',         // In Progress
                4: 'purple',       // Awaiting Review
                8: 'primary',      // Awaiting Deployment
                7: 'warning',      // Hold
                9: 'green',        // Deployed
                10: 'brown',       // Upcoming Task
                11: 'danger',      // Overdue Task
            };
            return statusColors[statusId] || 'secondary';
        }
    }

    // Initialize the subtask manager
    const subtaskManager = new SubtaskManager();
</script>

<style>
    .subtasks-wrapper {
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        padding: 20px;
    }

    .subtask-card {
        background: #fff;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        margin-bottom: 15px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.03);
    }

    .subtask-card:hover {
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
    }

    .subtask-header {
        padding: 12px 15px;
        cursor: pointer;
        transition: background-color 0.3s ease;
    }

    .subtask-header:hover {
        background-color: #f8f9fa;
    }

    .subtask-info {
        flex: 1;
    }

    .subtask-title {
        font-weight: 500;
        color: #2c3e50;
        font-size: 0.9rem;
        max-width: 200px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        position: relative;
        margin-right: 2px;
    }

    .subtask-title:hover::after {
        content: attr(title);
        position: absolute;
        left: 0;
        top: 100%;
        z-index: 1000;
        background: #333;
        color: #fff;
        padding: 5px 10px;
        border-radius: 4px;
        font-size: 0.85rem;
        white-space: normal;
        max-width: 300px;
        word-wrap: break-word;
    }

    .subtask-title i {
        margin-right: 8px;
        color: #6c757d;
    }

    .subtask-status {
        margin-left: auto;
        margin-right: 10px;
    }

    .badge {
        padding: 6px 12px;
        font-size: 0.75rem;
        font-weight: 500;
        white-space: nowrap;
        border-radius: 15px;
    }

    .badge-light-primary {
        background-color: #e8f0fe;
        color: #1976d2;
        border: 1px solid #e3f2fd;
        padding: 5px 12px;
        margin: 0 10px;
    }

    .badge-light-primary i {
        margin-right: 6px;
        font-size: 0.8rem;
    }

    .subtask-meta {
        display: flex;
        align-items: center;
        margin-top: 8px;
        font-size: 0.85rem;
        color: #6c757d;
    }

    .subtask-dates {
        color: #6c757d;
        font-size: 0.85rem;
        white-space: nowrap;
        padding: 0 15px;
    }

    .subtask-dates i {
        margin-right: 6px;
    }

    .action-group {
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .modern-name-tag {
        display: inline-flex;
        align-items: center;
        background: #f8f9fa;
        padding: 4px 10px;
        border-radius: 15px;
        font-size: 0.85rem;
    }

    .modern-name-tag i {
        margin-right: 5px;
        color: #6c757d;
    }

    .subtask-actions {
        margin-left: 8px;
    }

    .btn-danger-light {
        background-color: #fff;
        border: 1px solid #dc3545;
        color: #dc3545;
    }

    .btn-danger-light:hover {
        background-color: #dc3545;
        color: #fff;
    }

    /* Status badge colors */
    .badge-default { background-color: #f8f9fa; color: #6c757d; }
    .badge-success { background-color: #d4edda; color: #28a745; }
    .badge-info { background-color: #d1ecf1; color: #17a2b8; }
    .badge-purple { background-color: #e2d9f3; color: #6f42c1; }
    .badge-primary { background-color: #cce5ff; color: #007bff; }
    .badge-warning { background-color: #fff3cd; color: #ffc107; }
    .badge-green { background-color: #d4edda; color: #28a745; }
    .badge-brown { background-color: #f5e6d3; color: #856404; }
    .badge-danger { background-color: #f8d7da; color: #dc3545; }

    .checklist-container {
        padding: 15px;
        border-top: 1px solid #edf2f9;
        display: none;
    }

    .progress-wrapper {
        margin: 15px 0;
        background: #edf2f9;
        border-radius: 10px;
        padding: 15px;
    }

    .progress-bar {
        height: 6px;
        background: #edf2f9;
        border-radius: 3px;
        overflow: hidden;
    }

    .progress-fill {
        height: 100%;
        background: #5e72e4;
        border-radius: 3px;
        transition: width 0.3s ease;
    }

    .checklist-item {
        display: flex;
        align-items: center;
        padding: 8px;
        border-radius: 4px;
        margin-bottom: 8px;
        background: #f8f9fa;
        transition: all 0.2s ease;
    }

    .checklist-item:hover {
        background: #edf2f9;
    }

    .checklist-checkbox {
        width: 18px;
        height: 18px;
        margin-right: 12px;
    }

    .checklist-text {
        flex-grow: 1;
        font-size: 13px;
        color: #495057;
    }

    .checklist-text.completed {
        text-decoration: line-through;
        color: #8898aa;
    }

    .add-checklist-form {
        margin-top: 15px;
        display: flex;
        gap: 10px;
    }

    .add-checklist-input {
        flex-grow: 1;
        padding: 8px 12px;
        border: 1px solid #edf2f9;
        border-radius: 4px;
        font-size: 13px;
    }

    .add-checklist-btn {
        padding: 8px 16px;
        background: #5e72e4;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        transition: background 0.3s ease;
    }

    .add-checklist-btn:hover {
        background: #4558be;
    }

    .btn-danger-light {
        background: transparent;
        color: #dc3545;
        border: none;
        padding: 4px 8px;
        border-radius: 4px;
        transition: all 0.2s ease;
    }

    .btn-danger-light:hover {
        background: #dc3545;
        color: white;
    }

    .delete-checklist-btn {
        opacity: 0.5;
        transition: opacity 0.2s ease;
    }

    .checklist-item:hover .delete-checklist-btn {
        opacity: 1;
    }

    .checklist-text {
        margin-left: 8px;
        color: #344767;
    }

    .checklist-text.completed {
        text-decoration: line-through;
        color: #6c757d;
    }

    .name-badge {
        display: inline-block;
        padding: 4px 12px;
        background: #edf2f9;
        color: #2196f3;
        border-radius: 15px;
        font-size: 12px;
        font-weight: 500;
        border: 1px solid #e0e8f3;
        transition: all 0.2s ease;
    }

    .name-badge:hover {
        background: #e3ebf7;
        transform: translateY(-1px);
    }

    .name-badge.unassigned {
        background: #f1f3f4;
        color: #6c757d;
        border-color: #e9ecef;
    }

    .assignee-name {
        margin-top: 5px;
    }

    .modern-name-tag {
        display: inline-flex;
        align-items: center;
        background: linear-gradient(135deg, #2196f3, #1976d2);
        color: white;
        padding: 5px 12px;
        border-radius: 20px;
        font-size: 13px;
        font-weight: 500;
        box-shadow: 0 2px 4px rgba(33, 150, 243, 0.2);
        transition: all 0.3s ease;
    }

    .modern-name-tag:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(33, 150, 243, 0.3);
    }

    .modern-name-tag i {
        font-size: 14px;
        margin-right: 6px;
        opacity: 0.9;
    }

    .modern-name-tag .name {
        letter-spacing: 0.3px;
    }

    /* Keep existing assignee-avatar style for the fallback 'U' */
    .assignee-avatar {
        display: inline-block;
        width: 28px;
        height: 28px;
        line-height: 28px;
        text-align: center;
        background: #f1f3f4;
        color: #6c757d;
        border-radius: 50%;
        font-size: 12px;
    }

    .status-dropdown {
        cursor: pointer;
        user-select: none;
    }

    .status-dropdown-menu {
        min-width: 200px;
        padding: 8px 0;
        margin: 0;
        font-size: 0.875rem;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .status-dropdown-menu .dropdown-item {
        padding: 8px 16px;
        color: #495057;
        transition: background-color 0.2s ease;
    }

    .status-dropdown-menu .dropdown-item:hover {
        background-color: #f8f9fa;
        color: #16181b;
    }

    .status-select {
        padding: 2px 5px;
        border-radius: 4px;
        font-size: 12px;
        height: auto;
        width: auto;
        min-width: 120px;
        background-color: white;
        border: 1px solid #e9ecef;
    }

    .status-select:focus {
        border-color: #80bdff;
        outline: 0;
        box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
    }
</style>
<?php /**PATH C:\Content\Laravel\CRM-Grow\application\resources\views/pages/task/components/subtasks.blade.php ENDPATH**/ ?>