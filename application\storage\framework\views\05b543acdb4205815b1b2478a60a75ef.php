<!--task dependency-->
<div class="dependency-section">
    <div class="dependency-title">Task Dependencies</div>

    <!-- Enable Dependencies Checkbox -->
    <div class="dependency-checkbox-container">
        <input type="checkbox" id="add_dependency" name="add_dependency" class="filled-in chk-col-light-blue">
        <label for="add_dependency">Enable Dependencies</label>
    </div>

    <div class="col-sm-12 m-b-20">
        <button type="button" id="populateUsers" class="btn btn-info btn-sm hidden">
            Populate Assigned Users
        </button>
    </div>
</div>
<div id="dependency_content" class="hidden">
    <!-- Task Selection -->
    <div class="form-group row">
        <label class="col-sm-12 text-left control-label col-form-label required">Task</label>
        <div class="col-sm-12">
            <select class="select2-basic-search form-control form-control-sm" id="task_dependencies"
                name="task_dependencies" data-placeholder="Search Tasks...">
                <option></option>
                <?php $__currentLoopData = $tasks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $current_task): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e($current_task->task_id); ?>"><?php echo e($current_task->task_title); ?></option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </select>
        </div>
    </div>

    <!-- Dependency Type -->
    <div class="form-group row">
        <label class="col-sm-12 text-left control-label col-form-label required">Dependency Type</label>
        <div class="col-sm-12">
            <div class="radio radio-info">
                <input type="radio" name="dependency_type" id="dependency_blocked" value="blocked" checked>
                <label for="dependency_blocked">Blocked</label>
            </div>
            <div class="radio radio-info">
                <input type="radio" name="dependency_type" id="dependency_running" value="running">
                <label for="dependency_running">Running(Allocate Time)</label>
            </div>
        </div>
    </div>

    <!-- Timeline Section (Hidden by default) -->
    <div id="timelineSection" class="form-group row hidden">
        <label class="col-sm-12 text-left control-label col-form-label">Allocated Days</label>
        <div class="col-sm-12">
            <input type="number" class="form-control form-control-sm" id="timelineDays" name="timelineDays"
                min="1">
        </div>
    </div>

    <!-- Assign Users Section (Hidden by default) -->
    <div id="assignedUsersSection" class="form-group row hidden">
        <label class="col-sm-12 text-left control-label col-form-label">Assign Users</label>
        <div class="col-sm-12">
            <select class="select2-basic form-control form-control-sm" id="assigned_users_select"
                name="assigned_users[]" multiple>
            </select>
        </div>
    </div>

    <!-- Save Dependencies Button -->
    <div class="form-group text-right">
        <button type="button" class="btn btn-info btn-sm" id="saveDependencies">
            Save Dependency
        </button>
    </div>

    <!-- Saved Dependencies List Container -->
    <div class="mt-4">
        <h6>Added Dependencies</h6>
        <div id="saved-dependencies-list"></div>
    </div>
</div>

<script>
    $(document).ready(function() {
        // Initialize select2
        $("#task_dependencies").select2({
            placeholder: "Select a Task",
            allowClear: true
        });

        $("#assigned_users_select").select2({
            placeholder: "Select Users",
            allowClear: true
        });

        // Function to update hidden input with dependencies data
        function updateDependenciesInput() {
            const savedDependencies = JSON.parse(localStorage.getItem('taskDependencies') || '[]');

            const dependencyData = {
                is_dependency_enabled: $('#add_dependency').is(':checked'),
                dependencies: savedDependencies.map(dep => ({
                    task_id: dep.taskId,
                    dependency_type: dep.dependencyType,
                    allocated_days: dep.allocatedDays,
                    assigned_users: dep.assignedUsers
                }))
            };

            // Update hidden input
            $('#task_dependencies_data').val(JSON.stringify(dependencyData));

            // Update assigned users in the main form if checkbox is checked
            if ($('#add_dependency').is(':checked')) {
                updateAssignedUsersInForm(savedDependencies);
            }
        }

        // Function to update assigned users in the main form
        function updateAssignedUsersInForm(dependencies) {
            const mainFormAssignedSelect = $('#assigned');
            let currentUsers = mainFormAssignedSelect.val() || [];
            let newUsers = [...currentUsers];

            // Collect all unique users from dependencies
            dependencies.forEach(dep => {
                dep.assignedUsers.forEach(user => {
                    if (!newUsers.includes(user.id.toString())) {
                        newUsers.push(user.id.toString());

                        // Add option if it doesn't exist
                        if (!mainFormAssignedSelect.find(`option[value="${user.id}"]`).length) {
                            const newOption = new Option(user.text, user.id, true, true);
                            mainFormAssignedSelect.append(newOption);
                        }
                    }
                });
            });

            // Update the select2 with new values
            mainFormAssignedSelect.val(newUsers).trigger('change');
        }

        // Toggle dependency content
        $('#add_dependency').change(function() {
            $('#dependency_content').toggleClass('hidden', !this.checked);
            updateDependenciesInput();

            // Store original selection when checkbox is unchecked
            if (!this.checked) {
                const originalUsers = $(this).data('original-users') || [];
                $('#assigned').val(originalUsers).trigger('change');
            } else {
                // Store current selection as original when checking
                $(this).data('original-users', $('#assigned').val() || []);
            }
        });

        // Handle dependency type change
        $('input[name="dependency_type"]').change(function() {
            const isRunning = $(this).val() === 'running';
            $('#timelineSection').toggleClass('hidden', !isRunning);

            if (!isRunning) {
                $('#timelineDays').val('');
            }
        });

        // Handle task selection change
        $('#task_dependencies').on('change', function() {
            const taskId = $(this).val();
            if (taskId) {
                // Make API call to get task details
                $.ajax({
                    url: `/get-task-details/${taskId}`,
                    method: 'GET',
                    success: function(response) {
                        // Update task details
                        updateTaskDetailsView(response);

                        // Show assigned users section
                        $('#assignedUsersSection').removeClass('hidden');

                        // Update assigned users select
                        updateAssignedUsersSelect(response.users);
                    },
                    error: function(xhr, status, error) {
                        console.error('Error fetching task details:', error);
                        toastr.error('Error fetching task details');
                    }
                });
            } else {
                $('#assignedUsersSection').addClass('hidden');
                $('#assigned_users_select').empty();
            }
        });

        // Function to update task details view
        function updateTaskDetailsView(response) {
            const taskData = response.task;

            // Update any task-related information you want to display
            $('#selected-task-title').text(taskData.task_title || 'N/A');
            $('#selected-task-project').text(taskData.project_title || 'N/A');
            $('#selected-task-status').text(taskData.task_status || 'N/A');

            // Update timeline if needed
            let startDate = taskData.task_date_start ? moment(taskData.task_date_start).format('MMM DD, YYYY') :
                'N/A';
            let dueDate = taskData.task_date_due ? moment(taskData.task_date_due).format('MMM DD, YYYY') :
                'N/A';
            $('#selected-task-timeline').text(`${startDate} - ${dueDate}`);
        }

        // Function to update assigned users select
        function updateAssignedUsersSelect(users) {
            const $select = $('#assigned_users_select');
            $select.empty();

            // Add empty option
            $select.append(new Option('Select Users', '', false, false));

            // Add users to select
            Object.entries(users).forEach(([userId, userName]) => {
                const option = new Option(userName.toString(), userId.toString(), false, false);
                $select.append(option);
            });

            // Refresh Select2
            $select.trigger('change');
        }

        // Initialize Select2 for assigned users
        $('#assigned_users_select').select2({
            theme: "bootstrap",
            width: '100%',
            placeholder: "Select users",
            allowClear: true,
            dropdownParent: $('#taskDependencyModal')
        });

        // Function to get form data
        window.getFormData = function() {
            const selectedTask = $('#task_dependencies').select2('data')[0];
            if (!selectedTask) {
                toastr.error('Please select a task');
                return null;
            }

            const dependencyType = $('input[name="dependency_type"]:checked').val();
            const timelineDays = dependencyType === 'running' ? $('#timelineDays').val() : null;
            const selectedUsers = $('#assigned_users_select').select2('data');

            return {
                taskId: selectedTask.id,
                taskTitle: selectedTask.text,
                dependencyType: dependencyType,
                allocatedDays: timelineDays,
                assignedUsers: selectedUsers.map(user => ({
                    id: user.id,
                    text: user.text
                })),
                timestamp: new Date().getTime()
            };
        };

        // Function to display saved dependencies
        window.displaySavedDependencies = function() {
            const savedDependencies = JSON.parse(localStorage.getItem('taskDependencies') || '[]');
            const $list = $('#outerSavedDependenciesList');
            $list.empty();

            if (savedDependencies.length === 0) {
                $list.append('<div class="no-dependencies">No dependencies saved yet</div>');
                return;
            }

            savedDependencies.forEach((dep, index) => {
                const daysDisplay = dep.allocatedDays ? `${dep.allocatedDays} days` : '';
                const usersDisplay = dep.assignedUsers.map(user => user.text).join(', ');

                const html = `
                <div class="dependency-item card p-2 mb-2">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <strong>${dep.taskTitle}</strong>
                            <span class="badge badge-info ml-2">${dep.dependencyType}</span>
                            ${daysDisplay ? `<span class="badge badge-secondary ml-2">${daysDisplay}</span>` : ''}
                            ${usersDisplay ? `<div class="small text-muted mt-1">Users: ${usersDisplay}</div>` : ''}
                        </div>
                        <button type="button" class="btn btn-danger btn-sm delete-dependency" data-index="${index}">
                            <i class="sl-icon-trash"></i>
                        </button>
                    </div>
                </div>`;
                $list.append(html);
            });
        };

        // Initialize hidden input if not exists
        if (!$('#task_dependencies_data').length) {
            $('form').append('<input type="hidden" id="task_dependencies_data" name="task_dependencies_data">');
        }

        // Save dependency button click handler
        $('#saveDependencies').on('click', function() {
            if (!validateForm()) return;

            const dependency = getFormData();

            // Save to localStorage
            let dependencies = JSON.parse(localStorage.getItem('taskDependencies') || '[]');
            dependencies.push(dependency);
            localStorage.setItem('taskDependencies', JSON.stringify(dependencies));

            // Update display and hidden input
            displaySavedDependencies();
            updateDependenciesInput();

            // Clear form
            clearForm();

            toastr.success('Dependency added successfully');
        });

        function validateForm() {
            const selectedTask = $('#task_dependencies').select2('data')[0];
            if (!selectedTask) {
                toastr.error('Please select a task');
                return false;
            }

            const dependencyType = $('input[name="dependency_type"]:checked').val();
            if (!dependencyType) {
                toastr.error('Please select dependency type');
                return false;
            }

            if (dependencyType === 'running' && !$('#timelineDays').val()) {
                toastr.error('Please enter timeline days');
                return false;
            }

            const selectedUsers = $('#assigned_users_select').select2('data');
            if (selectedUsers.length === 0) {
                toastr.error('Please select at least one user');
                return false;
            }

            return true;
        }

        function displaySavedDependencies() {
            const dependencies = JSON.parse(localStorage.getItem('taskDependencies') || '[]');
            const $list = $('#saved-dependencies-list');
            $list.empty();

            if (dependencies.length === 0) {
                $list.append('<div class="no-dependencies">No dependencies added yet</div>');
                return;
            }

            dependencies.forEach((dep, index) => {
                const userNames = dep.assignedUsers.map(user => user.text).join(', ');

                const html = `
                    <div class="dependency-item">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <div class="d-flex align-items-center">
                                    <span class="text-dark">${dep.taskTitle}</span>
                                    <span class="dependency-type-badge">${dep.dependencyType}</span>
                                    ${dep.allocatedDays ? `<span class="dependency-days-badge">${dep.allocatedDays} days</span>` : ''}
                                </div>
                                <div class="dependency-users-info">
                                    <i class="sl-icon-people"></i> ${userNames}
                                </div>
                            </div>
                            <button type="button" 
                                title="<?php echo e(cleanLang(__('lang.delete'))); ?>"
                                class="data-toggle-action-tooltip btn btn-outline-danger btn-circle btn-sm confirm-action-danger remove-dependency"
                                data-confirm-title="<?php echo e(cleanLang(__('lang.delete_item'))); ?>"
                                data-confirm-text="<?php echo e(cleanLang(__('lang.are_you_sure'))); ?>"
                                data-index="${index}">
                                <i class="sl-icon-trash"></i>
                            </button>
                        </div>
                    </div>`;
                $list.append(html);
            });
        }

        function updateHiddenInput() {
            const dependencies = JSON.parse(localStorage.getItem('taskDependencies') || '[]');
            const data = {
                is_dependency_enabled: $('#add_dependency').is(':checked'),
                dependencies: dependencies.map(dep => ({
                    task_id: dep.taskId,
                    dependency_type: dep.dependencyType,
                    allocated_days: dep.allocatedDays,
                    assigned_users: dep.assignedUsers
                }))
            };
            $('#task_dependencies_data').val(JSON.stringify(data));
        }

        function clearForm() {
            $('#task_dependencies').val(null).trigger('change');
            $('#assigned_users_select').val(null).trigger('change');
            $('input[name="dependency_type"]').prop('checked', false);
            $('#timelineDays').val('');
        }

        // Remove dependency handler
        $(document).on('click', '.remove-dependency', function() {
            const index = $(this).data('index');
            let dependencies = JSON.parse(localStorage.getItem('taskDependencies') || '[]');
            dependencies.splice(index, 1);
            localStorage.setItem('taskDependencies', JSON.stringify(dependencies));

            displaySavedDependencies();
            updateDependenciesInput();
            // toastr.success('Dependency removed');
        });

        // Form submission handler
        $('form').on('submit', function() {
            updateDependenciesInput();
            // Clear localStorage after successful submission
            setTimeout(() => {
                localStorage.removeItem('taskDependencies');
            }, 100);
        });

        // Initialize display on page load
        displaySavedDependencies();
        updateDependenciesInput();
    });
</script>

<style>
    .dependency-section {
        border: 1px solid #e9ecef;
        border-radius: 4px;
        padding: 25px;
        margin: 20px 0;
        background-color: #fff;
    }

    .dependency-title {
        font-size: 16px;
        font-weight: 500;
        color: #2c3038;
        margin-bottom: 20px;
        padding-bottom: 12px;
        border-bottom: 1px solid #e9ecef;
    }

    .dependency-item {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 4px;
        padding: 15px;
        margin-bottom: 10px;
    }

    .dependency-type-badge {
        font-size: 11px;
        padding: 3px 8px;
        border-radius: 3px;
        background: #2196f3;
        color: white;
        margin-left: 8px;
    }

    .dependency-days-badge {
        font-size: 11px;
        padding: 3px 8px;
        border-radius: 3px;
        background: #6c757d;
        color: white;
        margin-left: 8px;
    }

    .dependency-users-info {
        font-size: 12px;
        color: #6c757d;
        margin-top: 8px;
    }

    .no-dependencies {
        text-align: center;
        padding: 15px;
        color: #6c757d;
        background: #f8f9fa;
        border-radius: 4px;
        font-size: 13px;
    }

    /* Hide the duplicate title */
    .dependency-section .dependency-header {
        display: none;
    }

    /* .hidden {
        display: none !important;
    }

    .m-l-10 {
        margin-left: 10px;
    }

    .m-b-0 {
        margin-bottom: 0;   
    }

    .m-b-20 {
        margin-bottom: 20px;
    } */

    .dependency-section {
        border: 2px dashed #ccd6e3;
        border-radius: 8px;
        padding: 20px;
        margin: 20px 0;
        background-color: #f8fafc;
        position: relative;
    }

    .dependency-section::before {
        content: 'Task Dependencies';
        position: absolute;
        top: -12px;
        left: 15px;
        background: #fff;
        padding: 0 10px;
        color: #6c757d;
        font-size: 0.9rem;
        font-weight: 500;
    }

    .dependency-item {
        background: white;
        border: 1px solid #e9ecef;
        border-radius: 6px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.03);
        transition: all 0.3s ease;
    }

    .dependency-item:hover {
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
        transform: translateY(-1px);
    }

    .dependency-type-badge {
        font-size: 0.75rem;
        padding: 4px 8px;
        border-radius: 12px;
    }

    .dependency-days-badge {
        background-color: #e9ecef;
        color: #495057;
    }

    .dependency-users-info {
        font-size: 0.8rem;
        color: #6c757d;
        margin-top: 5px;
        display: flex;
        align-items: center;
        gap: 5px;
    }

    .dependency-header {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 1px solid #e9ecef;
    }

    .dependency-header i {
        color: #2196f3;
        margin-right: 8px;
        font-size: 1.2rem;
    }

    .dependency-header h5 {
        margin: 0;
        color: #344767;
    }

    .no-dependencies {
        text-align: center;
        padding: 20px;
        color: #6c757d;
        font-style: italic;
        background: #fff;
        border-radius: 6px;
        border: 1px dashed #dee2e6;
    }

    /* Improve select2 appearance */
    .select2-container--bootstrap .select2-selection {
        border-color: #e9ecef;
    }

    /* Custom switch styling */
    .custom-switch {
        padding-left: 2.25rem;
    }

    .custom-control-label::before {
        border-radius: 1rem;
    }

    /* Add specific checkbox styling */
    .dependency-checkbox-container {
        margin-bottom: 20px;
    }

    .dependency-checkbox-container label {
        padding-left: 30px;
        color: #2c3038;
        font-weight: normal;
    }
</style>
<?php /**PATH C:\Content\Laravel\CRM-Grow\application\resources\views/pages/tasks/components/modals/task-dependency.blade.php ENDPATH**/ ?>