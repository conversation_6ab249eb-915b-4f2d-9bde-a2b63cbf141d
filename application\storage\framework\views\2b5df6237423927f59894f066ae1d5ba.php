<!--[dependency][lock-1] start-->
<?php if(config('visibility.task_is_open')): ?>
    <!----------Assigned----------->
    <?php if(config('visibility.tasks_card_assigned')): ?>
        <div class="x-section">
            <div class="x-title">
                <h6><?php echo e(cleanLang(__('lang.assigned_users'))); ?></h6>
            </div>
            <span id="task-assigned-container" class="">
                <?php echo $__env->make('pages.task.components.assigned', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            </span>
            <!--user-->
            <?php if($task->permission_assign_users): ?>
                <span class="x-assigned-user x-assign-new js-card-settings-button-static card-task-assigned text-info"
                    data-container=".card-modal" tabindex="0" data-popover-content="card-task-team"
                    data-title="<?php echo e(cleanLang(__('lang.assign_users'))); ?>"><i class="mdi mdi-plus"></i></span>
            <?php endif; ?>
        </div>
    <?php else: ?>
        <!--spacer-->
        <div class="p-b-40"></div>
    <?php endif; ?>


    <!--show timer-->
    <div id="task-timer-container">
        <?php echo $__env->make('pages.task.components.timer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    </div>


    <!----------settings----------->
    <div class="x-section">
        <div class="x-title">
            <h6><?php echo e(cleanLang(__('lang.settings'))); ?></h6>
        </div>
        <!--start date-->
        <?php if(config('visibility.tasks_standard_features')): ?>
            <div class="x-element" id="task-start-date"><i class="mdi mdi-calendar-plus task-startone"></i>
                <span><?php echo e(cleanLang(__('lang.start_date'))); ?>:</span>
                <?php if($task->permission_edit_task): ?>
                    <span class="x-highlight x-editable card-pickadate"
                        data-url="<?php echo e(urlResource('/tasks/' . $task->task_id . '/update-start-date/')); ?>"
                        data-type="form" data-progress-bar='hidden' data-form-id="task-start-date"
                        data-hidden-field="task_date_start" data-container="task-start-date-container"
                        data-ajax-type="post" id="task-start-date-container"><?php echo e(runtimeDate($task->task_date_start)); ?>

                    </span>
                    </span>


                    <input type="hidden" name="task_date_start" id="task_date_start">

                    <script>
                        $(document).ready(function() {
                            $('.card-pickadate').datepicker({
                                format: NX.date_picker_format,
                                language: "lang",
                                autoclose: true,
                                todayHighlight: true,
                                todayBtn: "linked"
                            });
                        });
                    </script>

                    <?php if(config('system.settings_tasks_null_date_start') == 'show'): ?>
                        <?php if(isProjectManager($task->task_projectid) && $task->task_date_start != null): ?>
                            <span
                                data-url="<?php echo e(urlResource('/tasks/' . $task->task_id . '/setDateNull/' . '?start_date=true')); ?>"
                                data-ajax-type="post" class="reset-date js-ajax-ux-request" id="reset-date"
                                title="Reset Date">
                                <i class="mdi mdi-refresh"></i>
                            </span>
                            <!--The Js is written at bottom of page.-->
                        <?php endif; ?>
                    <?php endif; ?>
                <?php else: ?>
                    <span class="x-highlight"><?php echo e(runtimeDate($task->task_date_start)); ?></span>
                <?php endif; ?>
            </div>

            <!--Working Days-->
            <?php if($task->task_started_date || $task->task_date_due): ?>
                <div class="x-element" id="task-working-days"><i class="mdi mdi-view-day"></i>
                    <span>Working Days:</span>
                    <?php if($task->permission_edit_task): ?>
                        <span class="x-highlight x-editable" data-type="text" data-progress-bar='hidden'
                            data-form-id="task-working-days" id="task-working-days">
                            
                            <?php if($task->task_date_start && $task->task_date_due): ?>
                                <?php echo e(calculateWorkingDays($task->task_date_start, $task->task_date_due)); ?> Days
                            <?php else: ?>
                                Days
                            <?php endif; ?>
                        </span>
                        <input type="hidden" name="task_working_days" id="task_working_days">
                    <?php else: ?>
                    <?php endif; ?>
                </div>

                <!--Working Hours-->
                <div class="x-element" id="task-working-hours"><i class="mdi mdi-av-timer"></i>
                    <span>Working Hours:</span>
                    <?php if($task->permission_edit_task): ?>
                        <span class="x-highlight x-editable" data-type="text" data-progress-bar='hidden'
                            data-form-id="task-working-hours" id="task-working-hours">
                            
                            <?php if($task->task_date_start && $task->task_date_due): ?>
                                <?php echo e(calculateWorkingHours($task->task_date_start, $task->task_date_due)); ?> Hours
                            <?php else: ?>
                                <?php echo e($task->working_hours); ?> Hours
                            <?php endif; ?>
                        </span>
                        <input type="hidden" name="task_working_hours" id="task_working_hours">
                    <?php else: ?>
                    <?php endif; ?>
                </div>
            <?php endif; ?>

        <?php endif; ?>
        <!--Same permission is checked for date,hours,days-->




        <!--due date-->
        <?php if(config('visibility.tasks_standard_features')): ?>
            <div class="x-element" id="task-due-date"><i class="mdi mdi-calendar-clock"></i>
                <span><?php echo e(cleanLang(__('lang.due_date'))); ?>:</span>
                <?php if($task->permission_edit_task): ?>
                    <span class="x-highlight x-editable card-pickadate"
                        data-url="<?php echo e(urlResource('/tasks/' . $task->task_id . '/update-due-date/')); ?>" data-type="form"
                        data-progress-bar='hidden' data-form-id="task-due-date" data-hidden-field="task_date_due"
                        data-container="task-due-date-container" data-ajax-type="post"
                        id="task-due-date-container"><?php echo e(runtimeDate($task->task_date_due)); ?></span></span>


                    <input type="hidden" name="task_date_due" id="task_date_due">



                    <?php if(config('system.settings_tasks_null_date_due') == 'show'): ?>
                        <?php if(isProjectManager($task->task_projectid) && $task->task_date_due != null): ?>
                            <span
                                data-url="<?php echo e(urlResource('/tasks/' . $task->task_id . '/setDateNull/' . '?due_date=true')); ?>"
                                data-ajax-type="post" class="reset-date js-ajax-ux-request" id="reset-date2"
                                title="Reset Date">
                                <i class="mdi mdi-refresh"></i>
                            </span>
                        <?php endif; ?>
                    <?php endif; ?>
                <?php else: ?>
                    <span class="x-highlight"><?php echo e(runtimeDate($task->task_date_due)); ?></span>
                <?php endif; ?>
            </div>
        <?php endif; ?>
        <!--status-->
        <div class="x-element" id="card-task-status"><i class="mdi mdi-flag"></i>
            <span><?php echo e(cleanLang(__('lang.status'))); ?>: </span>
            <?php if($task->permission_edit_task): ?>
                <span class="x-highlight x-editable js-card-settings-button-static" data-container=".card-modal"
                    id="card-task-status-text" tabindex="0" data-popover-content="card-task-statuses"
                    data-offset="0 25%" data-status-id="<?php echo e($task->taskstatus_id); ?>"
                    data-title="<?php echo e(cleanLang(__('lang.status'))); ?>"><?php echo e(runtimeLang($task->taskstatus_title)); ?></strong></span>
            <?php else: ?>
                <span class="x-highlight"><?php echo e(runtimeLang($task->taskstatus_title)); ?></span>
            <?php endif; ?>
        </div>

        <?php
            $activePhaseId = \App\Models\Task::where('task_id', $task->task_id)->value('active_phase_id');
            //yo active phase ko users lai matra dekhauna parxa..
            $phaseUsers = DB::table('task_phase_users')->where('task_phase_id', $activePhaseId)->pluck('user_id');
            ///NOW WE NEED TO SHOW THE TASKS TO CURRENT USERS ONLY I>E PHAASE USERS
            if ($activePhaseId) {
                $activePhase = $activePhaseId ? \App\Models\Phase::find($activePhaseId) : null;
                $activePhasePosition = $activePhase->position;
                $phases = \App\Models\Phase::where('position', '>', $activePhasePosition)->get();
                //sending tester phases
                $testerPhases = DB::table('phase_logs')
                    ->where('pushed_task_id', $task->task_id)
                    ->where('pushedtophase_id', 4)
                    ->pluck('pushedbyphase_id')
                    ->unique();
                if ($activePhaseId == 4) {
                    $phases = \App\Models\Phase::whereIn('id', $testerPhases)->get();
                    if ($phases->count() == 0) {
                        $phases = [];
                    }
                }
            }

            $selectedPhaseId = session('selected_phase_id');
            // $a=DB::table('task_phases')->where('task_id', $task->task_id)->where('phase_id',)->first();

            $selectedPhaseTaskStatusId = DB::table('task_phases')
                ->where('phase_id', $selectedPhaseId)
                ->where('phase_task_id', $task->task_id)
                ->value('phase_status_id');
            $selectedPhaseTaskStatus = App\Models\PhaseStatus::find($selectedPhaseTaskStatusId);
            $phaseStatuses = \App\Models\PhaseStatus::where('is_active', true)->get();
            //Get the current Selected Phase Status
        ?>

        <!--Phase Status-->
        <?php if($selectedPhaseTaskStatus): ?>
            <div class="x-element" id="card-task-status"><i class="mdi mdi-flag"></i>
                <span>Phase <?php echo e(cleanLang(__('lang.status'))); ?>: </span>
                <?php if($task->permission_edit_task): ?>
                    <select name="phase_status_id" id="phase_status_id"
                        class="select2-basic form-control form-control-sm" style="display: none;">
                        <?php $__currentLoopData = $phaseStatuses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $phaseStatus): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($phaseStatus->id); ?>"
                                <?php echo e($selectedPhaseTaskStatusId == $phaseStatus->id ? 'selected' : ''); ?>>
                                <?php echo e($phaseStatus->phasestatus_title); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                <?php else: ?>
                    <span class="x-highlight"><?php echo e(runtimeLang($selectedPhaseTaskStatus->phasestatus_title)); ?></span>
                <?php endif; ?>
            </div>

            <script>
                $(document).ready(function() {
                    // Show edit button and handle click
                    $('#edit-phase-status').click(function() {
                        $('#phase-status-text').hide();
                        $('#phase_status_id').show().focus();
                        $(this).hide();
                    });

                    // Send update immediately when status changes
                    $('#phase_status_id').change(function() {
                        const phaseStatusId = $(this).val();
                        const taskId = <?php echo e($task->task_id); ?>;
                        const phaseId = <?php echo e($selectedPhaseId); ?>;

                        // Show loading indicator
                        $(this).prop('disabled', true);

                        $.ajax({
                            url: '/tasks/update-phase-status',
                            method: 'POST',
                            headers: {
                                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                            },
                            data: {
                                task_id: taskId,
                                phase_id: phaseId,
                                phase_status_id: phaseStatusId
                            },
                            success: function(response) {
                                if (response.success) {
                                    // Update the displayed status text
                                    $('#phase-status-text').text(response.status_title);
                                    // Hide dropdown and show text
                                    $('#phase_status_id').hide().prop('disabled', false);
                                    $('#phase-status-text').show();
                                    $('#edit-phase-status').show();
                                    // Show success message
                                    toastr.success('Phase status updated successfully');
                                    window.location.reload();
                                }
                            },
                            error: function(xhr) {
                                // Re-enable dropdown on error
                                $('#phase_status_id').prop('disabled', false);
                                toastr.error('Failed to update phase status');
                                console.error(xhr.responseText);
                            }
                        });
                    });
                });
            </script>
        <?php endif; ?>


        

        <!--priority-->
        <div class="x-element" id="card-task-priority"><i class="mdi mdi-flag"></i>
            <span><?php echo e(cleanLang(__('lang.priority'))); ?>: </span>
            <?php if($task->permission_edit_task): ?>
                <span class="x-highlight x-editable js-card-settings-button-static" data-container=".card-modal"
                    id="card-task-priority-text" tabindex="0" data-popover-content="card-task-priorities"
                    data-offset="0 25%" data-status-id="<?php echo e($task->taskpriority_id); ?>"
                    data-title="<?php echo e(cleanLang(__('lang.priority'))); ?>"><?php echo e(runtimeLang($task->taskpriority_title)); ?></strong></span>
            <?php else: ?>
                <span class="x-highlight"><?php echo e(runtimeLang($task->taskpriority_title)); ?></span>
            <?php endif; ?>
        </div>

        <!--client visibility-->
        
        
        <?php
            $currentPhaseId = session('selected_phase_id');
            $currentPhase = \App\Models\TaskPhase::where('phase_id', $currentPhaseId)
                ->where('phase_task_id', $task->task_id)
                ->first();
            if ($currentPhase) {
                $phaseStartDate = $currentPhase->phase_start_date;
                $phaseEndDate = $currentPhase->phase_end_date;
            }else{
                $phaseStartDate = null;
                $phaseEndDate = null;
            }
        ?>


        
        



        <?php if(@$activePhaseId): ?>
            <?php if($activePhase): ?>
                <div class="x-element" id="task-active-phase">
                    <i class="mdi mdi-progress-check"></i>
                    <span>Active Phase:</span>
                    <span class="x-highlight"><?php echo e($activePhase->name); ?></span>
                </div>
            <?php endif; ?>

            <?php if($phaseStartDate): ?>
                <div class="x-element" id="task-phase-start-date"><i class="mdi mdi-calendar-plus"></i>
                    <span>Phase Start Date:</span>
                    <span class="x-highlight"><?php echo e(runtimeDate($phaseStartDate)); ?></span>
                </div>
            <?php endif; ?>
            <?php if($phaseEndDate): ?>
                <div class="x-element" id="task-phase-end-date"><i class="mdi mdi-calendar-clock"></i>
                    <span>Phase End Date:</span>
                    <span class="x-highlight"><?php echo e(runtimeDate($phaseEndDate)); ?></span>
                </div>
            <?php endif; ?>

            <?php if(auth()->user()->type == 'team'): ?>
                <div class="x-element" id="card-task-push"><i class="mdi mdi-send"></i>
                    <span>Push Phase:</span>
                    <?php if($task->permission_edit_task): ?>
                        <select name="task_phase" id="task_phase" class="select2-basic form-control form-control-sm">
                            <option value="">Select Phase</option>
                            <?php $__currentLoopData = $phases; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $phase): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($phase->id); ?>">
                                    <?php echo e($phase->name); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    <?php else: ?>
                    <?php endif; ?>
                </div>
                <link rel="stylesheet"
                    href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css">

                <?php if(isSuperAdmin()): ?>
                    <div class="x-element" id="card-task-users" style="display: none">
                        <span>Assign Users:</span>
                        <?php if($task->permission_edit_task): ?>
                            <select name="task_users[]" id="task_users"
                                class="select2-basic form-control form-control-sm" multiple>
                            </select>
                            <button type="button" class="btn btn-sm btn-primary mt-2" id="assignUsersBtn">
                                <i class="fas fa-check"></i>
                            </button>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>

                <script>
                    $(document).ready(function() {
                        let phaseId, selectedUsers;

                        $('#task_phase').change(function() {
                            phaseId = $(this).val();
                            taskId = <?php echo json_encode($task->task_id, 15, 512) ?>;
                            var currentPhase = <?php echo json_encode($activePhaseId, 15, 512) ?>;
                            const url = `phase/users/${phaseId}?currentPhase=${currentPhase}&taskId=${taskId}`;

                            sendRequest(url, 'GET')
                                .done(response => {
                                    document.getElementById('card-task-users').style.display = 'block';
                                    const taskAssignedUsers = <?php echo json_encode($task->assigned ?? []); ?>;
                                    let usersSelect = $('#task_users');
                                    usersSelect.empty();

                                    Object.entries(response.users).forEach(([userId, userName]) => {
                                        const isSelected = taskAssignedUsers.some(user => user.id ==
                                            userId);
                                        usersSelect.append(new Option(userName, userId, false, isSelected));
                                    });
                                    toastr.success('Phase updated successfully');
                                    window.location.reload();
                                    usersSelect.trigger('change');
                                })
                                .fail((xhr, status, error) => {
                                    if (xhr.responseJSON.error) {
                                        toastr.error(xhr.responseJSON.error);
                                    } else {
                                        toastr.error('Failed to fetch users. Please try again.');
                                    }
                                });
                        });

                        $('#assignUsersBtn').click(function() {
                            selectedUsers = $('#task_users').val();
                            if (!selectedUsers || selectedUsers.length === 0) {
                                toastr.error('Please select at least one user before assigning.');
                                return;
                            }

                            var task_id = <?php echo json_encode($task->task_id, 15, 512) ?>;
                            var currentPhase = <?php echo json_encode($activePhaseId, 15, 512) ?>;
                            var status = <?php echo json_encode($task->task_status, 15, 512) ?>;

                            const url =
                                `pushPhaseUsers/?phaseId=${phaseId}&users=${selectedUsers}&task_id=${task_id}&currentPhase=${currentPhase}`;
                            sendRequest(url, 'GET')
                                .done(response => {
                                    toastr.success('Users assigned successfully');
                                    window.location.reload();
                                })
                                .fail((xhr, status, error) => {
                                    toastr.error(xhr.responseJSON.message,
                                        'Failed to assign users. Please try again.');
                                });
                        });
                    });
                </script>
            <?php endif; ?>

        <?php endif; ?>

        <?php
            //project ko module matra dekhaune.
            $modules = \App\Models\Module::where('project_id', $task->task_projectid)->get();
            $workspace = \App\Models\Tasks\Workspace::where('id', $task->workspace_id)->first();
        ?>
        <!--Workspace Naming-->
        <?php if(auth()->user()->type == 'team' && isset($workspace)): ?>
            <div class="x-element" id="card-task-client-workspace"><i class="mdi mdi-eye"></i>
                <span>Workspaces:</span>
                <?php if($task->permission_edit_task): ?>
                    <span class="x-highlight x-editable js-card-settings-button-static" data-container=".card-modal"
                        id="card-task-client-workspace-text" tabindex="0"
                        data-popover-content="card-task-workspace"
                        data-title="Workspace: "><?php echo e($workspace->workspace_name); ?></strong></span>
                    <input type="hidden" name="task_client_workspace" id="task_client_workspace">
                <?php else: ?>
                    <span
                        class="x-highlight"><?php echo e(runtimeDBlang($workspace->workspace_name, 'task_client_workspace')); ?></span>
                <?php endif; ?>
            </div>
        <?php endif; ?>


        <!--Current Task Logs-->
        <?php if(isSuperAdmin()): ?>
            <div class="text-center my-3">
                <button onclick="sendtoTaskLog()" type="button" class="btn btn-outline-primary btn-sm">
                    View Task Logs
                </button>
            </div>
        <?php endif; ?>



        <!--Module visibility-->
        
        <?php if(auth()->user()->type == 'team'): ?>
            <div title="Only Active Module are  Listed" class="x-element" id="card-module-visibility">
                <span>
                    <h5 style="padding-left: 68px text-decoration: underline; text-decoration-style: dotted;">
                        Module<h4>
                </span>
                
                <?php if($task->permission_edit_task): ?>
                    <select name="task_module" id="task_module" class="select2-basic form-control form-control-sm">
                        <option value="">SELECT MODULE</option>
                        
                        <?php if($task->module): ?>
                            <?php $__currentLoopData = $modules; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $module): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option <?php echo e($module->id == $task->module->id ? 'selected' : ''); ?>

                                    value="<?php echo e($module->id); ?>">
                                    <?php echo e($module->name); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            
                        <?php else: ?>
                            <?php $__currentLoopData = $modules; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $module): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($module->id); ?>">
                                    <?php echo e($module->name); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php endif; ?>

                    </select>
                <?php endif; ?>
            </div>
        <?php endif; ?>



        <!--reminder-->
        <?php if(config('visibility.modules.reminders') && $task->project_type == 'project'): ?>
            <div class="card-reminders-container" id="card-reminders-container">
                <?php echo $__env->make('pages.reminders.cards.wrapper', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            </div>
        <?php endif; ?>


    </div>

    <!----------tags----------->
    <div class="card-tags-container" id="card-tags-container">
        <?php echo $__env->make('pages.task.components.tags', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    </div>
<?php endif; ?>
<!--[dependency][lock-1] end-->

<!--[dependency][lock-2] start-->
<?php if(config('visibility.task_is_locked')): ?>
    <!--spacer-->
    <div class="p-t-15"></div>
<?php endif; ?>
<!--[dependency][lock-2] end-->


<!--dependencies-->
<div class="x-section">
    <div class="x-title">
        <h6><?php echo e(cleanLang(__('lang.dependencies'))); ?></h6>
    </div>
    <?php echo $__env->make('pages.task.dependency.wrapper', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
</div>


<!--[dependency][lock-3] start-->
<?php if(config('visibility.task_is_open')): ?>

    <!----------actions----------->
    <div class="x-section">
        <div class="x-title">
            <h6><?php echo e(cleanLang(__('lang.actions'))); ?></h6>
        </div>

        <!--track if we have any actions-->
        <?php $count_action = 0 ; ?>

        <!--change milestone-->
        <?php if($task->permission_edit_task && auth()->user()->type == 'team'): ?>
            <div class="x-element x-action js-card-settings-button-static" data-container=".card-modal"
                id="card-task-milestone" tabindex="0" data-popover-content="card-task-milestones"
                data-title="<?php echo e(cleanLang(__('lang.milestone'))); ?>"><i class="mdi mdi-redo-variant"></i>
                <span class="x-highlight"><?php echo e(cleanLang(__('lang.change_milestone'))); ?></strong></span>
            </div>
            <?php $count_action ++ ; ?>
        <?php endif; ?>

        <!--stop all timer-->
        <?php if($task->permission_super_user && config('visibility.tasks_standard_features')): ?>
            <div class="x-element x-action confirm-action-danger"
                data-confirm-title="<?php echo e(cleanLang(__('lang.stop_all_timers'))); ?>"
                data-confirm-text="<?php echo e(cleanLang(__('lang.are_you_sure'))); ?>"
                data-url="<?php echo e(url('/')); ?>/tasks/timer/<?php echo e($task->task_id); ?>/stopall?source=card"><i
                    class="mdi mdi-timer-off"></i>
                <span class="x-highlight"
                    id="task-start-date"><?php echo e(cleanLang(__('lang.stop_all_timers'))); ?></span></span>
            </div>
            <?php $count_action ++ ; ?>
        <?php endif; ?>


        <!--archive-->
        <?php if($task->permission_edit_task && config('visibility.tasks_standard_features')): ?>
            <div class="x-element x-action confirm-action-info  <?php echo e(runtimeActivateOrAchive('archive-button', $task->task_active_state)); ?> card_archive_button_<?php echo e($task->task_id); ?>"
                id="card_archive_button_<?php echo e($task->task_id); ?>"
                data-confirm-title="<?php echo e(cleanLang(__('lang.archive_task'))); ?>"
                data-confirm-text="<?php echo e(cleanLang(__('lang.are_you_sure'))); ?>" data-ajax-type="PUT"
                data-url="<?php echo e(url('/')); ?>/tasks/<?php echo e($task->task_id); ?>/archive"><i class="mdi mdi-archive"></i>
                <span class="x-highlight" id="task-start-date"><?php echo e(cleanLang(__('lang.archive'))); ?></span></span>
            </div>
            <?php $count_action ++ ; ?>
        <?php endif; ?>

        <!--restore-->
        <?php if($task->permission_edit_task && runtimeArchivingOptions()): ?>
            <div class="x-element x-action confirm-action-info  <?php echo e(runtimeActivateOrAchive('activate-button', $task->task_active_state)); ?> card_restore_button_<?php echo e($task->task_id); ?>"
                id="card_restore_button_<?php echo e($task->task_id); ?>"
                data-confirm-title="<?php echo e(cleanLang(__('lang.restore_task'))); ?>"
                data-confirm-text="<?php echo e(cleanLang(__('lang.are_you_sure'))); ?>" data-ajax-type="PUT"
                data-url="<?php echo e(url('/')); ?>/tasks/<?php echo e($task->task_id); ?>/activate"><i class="mdi mdi-archive"></i>
                <span class="x-highlight" id="task-start-date"><?php echo e(cleanLang(__('lang.restore'))); ?></span></span>
            </div>
            <?php $count_action ++ ; ?>
        <?php endif; ?>

        <!--delete-->
        <?php if($task->permission_delete_task && runtimeArchivingOptions()): ?>
            <div class="x-element x-action confirm-action-danger"
                data-confirm-title="<?php echo e(cleanLang(__('lang.delete_item'))); ?>"
                data-confirm-text="<?php echo e(cleanLang(__('lang.are_you_sure'))); ?>" data-ajax-type="DELETE"
                data-url="<?php echo e(urlResource('/tasks/' . $task->task_id)); ?>"><i class="mdi mdi-delete"></i>
                <span class="x-highlight" id="task-start-date"><?php echo e(cleanLang(__('lang.delete'))); ?></span></span>
            </div>
            <?php $count_action ++ ; ?>
        <?php endif; ?>


        <!--no action available-->
        <?php if($count_action == 0): ?>
            <div class="x-element">
                <?php echo e(cleanLang(__('lang.no_actions_available'))); ?>

            </div>
        <?php endif; ?>

    </div>

    <!----------meta infor----------->
    <div class="x-section">
        <div class="x-title">
            <h6><?php echo e(cleanLang(__('lang.information'))); ?></h6>
        </div>
        <div class="x-element x-action">
            <table class="table table-bordered table-sm">
                <tbody>
                    <tr>
                        <td><?php echo e(cleanLang(__('lang.task_id'))); ?></td>
                        <td><strong>#<?php echo e($task->task_id); ?></strong></td>
                    </tr>
                    <tr>
                        <td><?php echo e(cleanLang(__('lang.created_by'))); ?></td>
                        <td><strong><?php echo e($task->first_name); ?> <?php echo e($task->last_name); ?></strong></td>
                    </tr>
                    <tr>
                        <td><?php echo e(cleanLang(__('lang.date_created'))); ?></td>
                        <td><strong><?php echo e(runtimeDate($task->task_created)); ?></strong></td>
                    </tr>
                    <?php if(auth()->user()->is_team): ?>
                        <tr>
                            <td><?php echo e(cleanLang(__('lang.total_time'))); ?></td>
                            <td><strong><span
                                        id="task_timer_all_card_<?php echo e($task->task_id); ?>"><?php echo clean(runtimeSecondsHumanReadable($task->sum_all_time, false)); ?></span></strong>
                            </td>
                        </tr>
                        <tr>
                            <td><?php echo e(cleanLang(__('lang.time_invoiced'))); ?></td>
                            <td><strong><span
                                        id="task_timer_all_card_<?php echo e($task->task_id); ?>"><?php echo clean(runtimeSecondsHumanReadable($task->sum_invoiced_time, false)); ?></span></strong>
                            </td>
                        </tr>
                        <tr>
                            <td><?php echo e(cleanLang(__('lang.project'))); ?></td>
                            <td><strong><a href="<?php echo e(urlResource('/projects/' . $task->task_projectid)); ?>"
                                        target="_blank">#<?php echo e($task->project_id); ?></a></strong>
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
<?php else: ?>
    <!--just a spacer for dependencied-->
    <div class="p-b-100 p-t-100"></div>

<?php endif; ?>
<!--[dependency][lock-3] end-->




<!-----------------------------popover dropdown elements------------------------------------>

<!--task statuses - popover -->
<?php
    $session = session('workspace_id');
    $workspace_id = $task->workspace_id;
    if ($session || $workspace_id) {
        $workspace = \App\Models\Tasks\Workspace::find($session ? $session : $workspace_id);
        $statuses = $workspace->taskStatuses;
    }
?>
<?php if($task->permission_participate): ?>
    <div class="hidden" id="card-task-statuses">
        <ul class="list">
            <?php if(isset($statuses)): ?>
                <?php $__currentLoopData = $statuses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $task_status): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <li class="card-tasks-update-status-link" data-button-text="card-task-status-text"
                        data-progress-bar='hidden'
                        data-url="<?php echo e(urlResource('/tasks/' . $task->task_id . '/update-status')); ?>" data-type="form"
                        data-value="<?php echo e($task_status->taskstatus_id); ?>" data-form-id="--set-dynamically--"
                        data-ajax-type="post">
                        <?php echo e(runtimeLang($task_status->taskstatus_title)); ?></li>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php else: ?>
                <?php $__currentLoopData = config('task_statuses'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $task_status): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <li class="card-tasks-update-status-link" data-button-text="card-task-status-text"
                        data-progress-bar='hidden'
                        data-url="<?php echo e(urlResource('/tasks/' . $task->task_id . '/update-status')); ?>" data-type="form"
                        data-value="<?php echo e($task_status->taskstatus_id); ?>" data-form-id="--set-dynamically--"
                        data-ajax-type="post">
                        <?php echo e(runtimeLang($task_status->taskstatus_title)); ?></li>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php endif; ?>
        </ul>
        <input type="hidden" name="task_status" id="task_status">



        <input type="hidden" name="current_task_status_text" id="current_task_status_text">


    </div>
<?php endif; ?>


<!--task priorities - popover -->
<?php if($task->permission_participate): ?>
    <div class="hidden" id="card-task-priorities">
        <ul class="list">
            <?php $__currentLoopData = config('task_priorities'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $priority): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <li class="card-tasks-update-priority-link" data-button-text="card-task-priority-text"
                    data-progress-bar='hidden'
                    data-url="<?php echo e(urlResource('/tasks/' . $task->task_id . '/update-priority')); ?>" data-type="form"
                    data-value="<?php echo e($priority->taskpriority_id); ?>" data-form-id="--set-dynamically--"
                    data-ajax-type="post">
                    <?php echo e(runtimeLang($priority->taskpriority_title)); ?></li>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </ul>
        <input type="hidden" name="task_priority" id="task_priority">


        <input type="hidden" name="current_task_priority_text" id="current_task_priority_text">

    </div>
<?php endif; ?>


<!--task priority - popover-->
<?php if($task->permission_participate): ?>
    <div class="hidden" id="card-task-priorities">
        <ul class="list">
            <?php $__currentLoopData = config('settings.task_priority'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <li class="card-tasks-update-priority-link" data-button-text="card-task-priority-text"
                    data-progress-bar='hidden'
                    data-url="<?php echo e(urlResource('/tasks/' . $task->task_id . '/update-priority')); ?>" data-type="form"
                    data-value="<?php echo e($key); ?>" data-form-id="--set-dynamically--" data-ajax-type="post">
                    <?php echo e(runtimeLang($key)); ?></li>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </ul>
        <input type="hidden" name="task_priority" id="task_priority">
        <input type="hidden" name="current_task_priority_text" id="current_task_priority_text">
    </div>
<?php endif; ?>

<!--client visibility - popover-->
<?php if($task->permission_edit_task): ?>
    <div class="hidden" id="card-task-visibility">
        <ul class="list">
            <li class="card-tasks-update-visibility-link" data-button-text="card-task-client-visibility-text"
                data-progress-bar='hidden'
                data-url="<?php echo e(urlResource('/tasks/' . $task->task_id . '/update-visibility')); ?>" data-type="form"
                data-value="no" data-text="<?php echo e(cleanLang(__('lang.hidden'))); ?>"
                data-form-id="card-task-client-visibility" data-ajax-type="post">
                <?php echo e(cleanLang(__('lang.hidden'))); ?>

            </li>
            <li class="card-tasks-update-visibility-link" data-button-text="card-task-client-visibility-text"
                data-progress-bar='hidden'
                data-url="<?php echo e(urlResource('/tasks/' . $task->task_id . '/update-visibility')); ?>" data-type="form"
                data-value="yes" data-text="<?php echo e(cleanLang(__('lang.visible'))); ?>"
                data-form-id="card-task-client-visibility" data-ajax-type="post">
                <?php echo e(cleanLang(__('lang.visible'))); ?>

            </li>
        </ul>
        <input type="hidden" name="task_client_visibility" id="task_client_visibility">
        <input type="hidden" name="current_task_client_visibility_text" id="current_task_client_visibility_text">
    </div>
<?php endif; ?>




<!--milestone - popover -->
<?php if($task->permission_edit_task): ?>
    <div class="hidden" id="card-task-milestones">
        <div class="form-group m-t-10">
            <select class="custom-select col-12 form-control form-control-sm" id="task_milestoneid"
                name="task_milestoneid">
                <?php if(isset($milestones)): ?>
                    <?php $__currentLoopData = $milestones; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $milestone): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($milestone->milestone_id); ?>">
                            <?php echo e(runtimeLang($milestone->milestone_title, 'task_milestone')); ?></option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php endif; ?>
            </select>
        </div>
        <div class="form-group text-right">
            <button type="button" class="btn btn-danger btn-sm" id="card-tasks-update-milestone-button"
                data-progress-bar='hidden'
                data-url="<?php echo e(urlResource('/tasks/' . $task->task_id . '/update-milestone')); ?>" data-type="form"
                data-ajax-type="post" data-form-id="popover-body">
                <?php echo e(cleanLang(__('lang.update'))); ?>

            </button>
        </div>
    </div>
<?php endif; ?>


<!--assign user-->
<div class="hidden" id="card-task-team">
    <div class="card-assigned-popover-content">
        <div class="alert alert-info">Only users assigned to the project are shown in this list</div>
        <div class="line"></div>

        <!--staff users-->
        <h5><?php echo app('translator')->get('lang.team_members'); ?></h5>
        <?php $__currentLoopData = $project_assigned; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $staff): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="form-check m-b-15">
                <label class="custom-control custom-checkbox">
                    <input type="checkbox" name="assigned[<?php echo e($staff->id); ?>]"
                        class="custom-control-input assigned_user_<?php echo e($staff->id); ?>">
                    <span class="custom-control-indicator"></span>
                    <span class="custom-control-description"><img
                            src="<?php echo e(getUsersAvatar($staff->avatar_directory, $staff->avatar_filename)); ?>"
                            class="img-circle avatar-xsmall"> <?php echo e($staff->first_name); ?>

                        <?php echo e($staff->last_name); ?></span>
                </label>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

        <div class="line"></div>

        <!--client users-->
        <h5><?php echo app('translator')->get('lang.client_users'); ?></h5>
        <?php $__currentLoopData = $client_users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $staff): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="form-check m-b-15">
                <label class="custom-control custom-checkbox">
                    <input type="checkbox" name="assigned[<?php echo e($staff->id); ?>]"
                        class="custom-control-input assigned_user_<?php echo e($staff->id); ?>">
                    <span class="custom-control-indicator"></span>
                    <span class="custom-control-description"><img
                            src="<?php echo e(getUsersAvatar($staff->avatar_directory, $staff->avatar_filename)); ?>"
                            class="img-circle avatar-xsmall"> <?php echo e($staff->first_name); ?>

                        <?php echo e($staff->last_name); ?></span>
                </label>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

        <div class="form-group text-right">
            <button type="button" class="btn btn-danger btn-sm" id="card-tasks-update-assigned"
                data-progress-bar='hidden'
                data-url="<?php echo e(urlResource('/tasks/' . $task->task_id . '/update-assigned')); ?>" data-type="form"
                data-ajax-type="post" data-form-id="popover-body">
                <?php echo e(cleanLang(__('lang.update'))); ?>

            </button>
        </div>
    </div>
</div>


<script src="public/js/dynamic/ajaxDynamic.js"></script>


<script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
<link href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css" rel="stylesheet" />


<script>
    $(document).ready(function() {
        //We will disable the select 2 of taskModule 
        $('#task_module').select2();
        $('#task_submodule').prop('disabled', true);

        $('#task_module').change(function() {
            $('#task_submodule').prop('disabled', false);

            var selectedModuleId = $(this).val();
            const taskId = <?php echo json_encode($task->task_id, 15, 512) ?>;
            const projectId = <?php echo json_encode($task->task_projectid, 15, 512) ?>;

            var parenturl = '/tasks/updateModule?module_id=' + selectedModuleId + '&task_id=' + taskId +
                '&project_id=' + projectId;

            //For Updating the Main Module
            sendRequest(parenturl, 'GET')
                .done(response => {
                    //appending the data to the #submodule select options
                    $('#task_submodule').empty();
                    $('#task_submodule').append(new Option('', ''));
                    $.each(response, function(index, item) {
                        $('#task_submodule').append(new Option(item.name, item.id));
                    });
                    toastr.success('Module Updated Successfully!');

                })
                .fail((xhr, status, error) => {
                    toastr.error('Action failed. Please try again!');
                    console.log(error);
                });

            // $.ajax({
            //     url: url,
            //     type: 'GET',
            //     success: function(response) {
            //         alert('sucess');
            //     },
            //     error: function(xhr, status, error) {
            //         console.error(error);
            //         alert('An error occurred!');
            //     }
            // });
        });

        $('#task_submodule').change(function() {
            var selectedSubmoduleId = $(this).val();
            const taskId = <?php echo json_encode($task->task_id, 15, 512) ?>;

            var childurl = '/tasks/updateModule?subModule_id=' + selectedSubmoduleId + '&task_id=' +
                taskId +
                '&is_submodule=true';

            //For updating the Sub Modules
            sendRequest(childurl, 'GET')
                .done(response => {
                    toastr.success('Sub Module Updated Successfully!');
                })
                .fail((xhr, status, error) => {
                    toastr.error('Action failed. Please try again!');
                });
        })

        document.getElementById('reset-date').addEventListener('click', function(e) {
            document.getElementById('task-start-date-container').style.display = 'none';
            document.getElementById('reset-date').style.display = 'none';
        });

        document.getElementById('reset-date2').addEventListener('click', function(e) {
            document.getElementById('task-due-date-container').style.display = 'none';
            document.getElementById('reset-date2').style.display = 'none';
        });


        // sending to the taskLogs current taskId


    });

    function sendtoTaskLog() {
        const taskId = <?php echo json_encode($task->task_id, 15, 512) ?>;
        const modalSession = localStorage.getItem('modalSession');
        // alert(sessionId);


        const url = 'report/tasks/saveTaskLogId?taskId=' + taskId;

        const redirect_url = 'reports/tasks/tasklogs';

        /// yo paro kinaki user le modal banda nagarera direct log herna khojna ni sakxa so...
        // const clear_url = 'report/tasks/clearModalSession'

        // $.ajax({
        //     url: clear_url,
        //     type: 'GET',
        //     success: function(response) {
        //         console.log('Session cleared successfully');
        //     },
        //     error: function(xhr, status, error) {
        //         console.error('Error:', error);
        //     }
        // });
        ////////////////////////////////////////////






        //page refresh vaihalxa so pahila laravel ko session ma save garam so ajax t hannei paro client side ma hanera tanna garho hunxa.   
        sendRequest(url, 'GET')
            .done(response => {
                if (response == 1) {
                    window.location.href = redirect_url;

                }
            })
            .fail((xhr, status, error) => {
                console.error('Error:', error);
            });
        // window.location.href = url;
    }

    // $('#cardModal').on('hidden.bs.modal', function() {
    //     const clear_url = 'report/tasks/clearModalSession'

    //     $.ajax({
    //         url: clear_url,
    //         type: 'GET',
    //         success: function(response) {
    //             console.log('Session cleared successfully');
    //         },
    //         error: function(xhr, status, error) {
    //             console.error('Error:', error);
    //         }
    //     });
    // });

    //     function sendData() {
    //     const taskId = <?php echo json_encode($task->task_id, 15, 512) ?>;

    //     // Get current values from the form fields
    //     const taskDateStart = document.querySelector('[name="task_date_start"]')?.value || 'Not Found';
    //     const taskDateDue = document.querySelector('[name="task_date_due"]')?.value || 'Not Found';

    //     // Get status from the status text element
    //     const taskStatus = document.getElementById('card-task-status-text')?.textContent.trim() || 'Not Found';

    //     // Get priority from the priority text element (assuming similar structure to status)
    //     const taskPriority = document.getElementById('card-task-priority-text')?.textContent.trim() || 'Not Found';

    //     // Get client visibility
    //     const taskClientVisibility = document.getElementById('card-task-client-visibility-text')?.textContent.trim() || 'Not Found';

    //     // Log current values
    //     console.log('Current Values:');
    //     console.log('---------------');
    //     console.log('Task ID:', taskId);
    //     console.log('Start Date:', taskDateStart);
    //     console.log('Due Date:', taskDateDue);
    //     console.log('Status:', taskStatus);
    //     console.log('Priority:', taskPriority);
    //     console.log('Client Visibility:', taskClientVisibility);
    // }
</script>
<script>
    $(document).ready(function() {
        // Destroy any existing datepicker instances first
        $('.card-pickadate').datepicker('destroy');

        // Reinitialize with explicit options
        $('.card-pickadate').datepicker({
            format: NX.date_picker_format,
            language: "lang",
            autoclose: true,
            todayHighlight: true,
            todayBtn: "linked",
            clearBtn: true,
            orientation: "bottom auto"
        });

        // Force refresh of the datepicker
        $('.card-pickadate').datepicker('update');
    });
</script>
<?php /**PATH C:\Content\Laravel\CRM-Grow\application\resources\views/pages/task/rightpanel.blade.php ENDPATH**/ ?>