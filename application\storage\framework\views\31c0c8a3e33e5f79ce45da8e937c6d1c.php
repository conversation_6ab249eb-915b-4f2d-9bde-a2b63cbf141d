    <?php $__currentLoopData = $tasks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $task): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <?php if($task->assigned_to_me || $task->task_creatorid == auth()->user()->id || auth()->user()->is_admin): ?>

            <!--each row-->
            <tr id="task_<?php echo e($task->task_id); ?>"
                class="<?php echo e(runtimeTaskCompletedStatus($task->task_status)); ?> <?php echo e($task->pinned_status ?? ''); ?> main-task-row">
                <td class="tasks_col_title td-edge">
                    <!--for polling timers-->
                    <input type="hidden" name="tasks[<?php echo e($task->task_id); ?>]" value="<?php echo e($task->assigned_to_me); ?>">
                    <!--checkbox-->
                    <span class="task_border td-edge-border bg-<?php echo e($task->taskstatus_color); ?>"></span>
                    
                    
                    <?php if(isset($task->subtasks) && count($task->subtasks) > 0): ?>
                        <div class="subtask-expander" data-task-id="<?php echo e($task->task_id); ?>" style="display: inline-block; margin-right: 10px;">
                            <button type="button" 
                                    class="btn btn-sm subtask-toggle-btn" 
                                    onclick="toggleTaskSubtasks(<?php echo e($task->task_id); ?>)"
                                    data-toggle="tooltip" 
                                    data-placement="top"
                                    title="Show <?php echo e(count($task->subtasks)); ?> subtask(s)"
                                    style="background: none; border: none; padding: 2px 6px; border-radius: 4px; transition: all 0.3s ease;">
                                <i class="fas fa-chevron-right subtask-chevron" 
                                   id="chevron-<?php echo e($task->task_id); ?>" 
                                   style="font-size: 12px; color: #007bff; transition: transform 0.3s ease;"></i>
                                <span class="subtask-count badge badge-primary badge-pill ml-1" 
                                      style="font-size: 10px; background: #007bff;"><?php echo e(count($task->subtasks)); ?></span>
                            </button>
                        </div>
                    <?php endif; ?>
                    
                    <?php if(config('visibility.tasks_checkbox')): ?>
                        <span class="list-checkboxes m-l-0">
                            <input type="checkbox" id="toggle_task_status_<?php echo e($task->task_id); ?>" name="toggle_task_status"
                                class="toggle_task_status filled-in chk-col-light-blue js-ajax-ux-request-default"
                                data-url="<?php echo e(urlResource('/tasks/' . $task->task_id . '/toggle-status')); ?>"
                                data-ajax-type="post" data-type="form" data-form-id="task_<?php echo e($task->task_id); ?>"
                                data-notifications="disabled" data-container="task_<?php echo e($task->task_id); ?>"
                                data-progress-bar="hidden" data-task-status="<?php echo e($task->task_status); ?>"
                                <?php echo e(runtimePrechecked($task->task_status)); ?>

                                <?php echo e(runtimeTaskDependencyLock($task->count_dependency_cannot_complete)); ?>>

                            <label for="toggle_task_status_<?php echo e($task->task_id); ?>">
                                <a class="show-modal-button reset-card-modal-form js-ajax-ux-request"
                                    href="javascript:void(0)" data-toggle="modal" data-target="#cardModal"
                                    data-url="<?php echo e(urlResource('/tasks/' . $task->task_id)); ?>"
                                    data-loading-target="main-top-nav-bar"><span class="x-strike-through"
                                        id="table_task_title_<?php echo e($task->task_id); ?>">
                                        <?php echo e(str_limit($task->task_title ?? '---', 40)); ?></span>
                                    <!--various icons displayed next to title-->
                                    <?php echo $__env->make('pages.tasks.components.common.icons-1', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                </a>
                            </label>
                        </span>
                    <?php endif; ?>
                    <?php if(config('visibility.tasks_nocheckbox')): ?>
                        <a class="show-modal-button reset-card-modal-form js-ajax-ux-request p-l-5"
                            href="javascript:void(0)" data-toggle="modal" data-target="#cardModal"
                            data-url="<?php echo e(urlResource('/tasks/' . $task->task_id)); ?>"
                            data-loading-target="main-top-nav-bar"><span class="x-strike-through"
                                id="table_task_title_<?php echo e($task->task_id); ?>">
                                <?php echo e(str_limit($task->task_title ?? '---', 45)); ?></span>
                            <!--recurring-->
                            <?php if(auth()->user()->is_team && $task->task_recurring == 'yes'): ?>
                                <span class="sl-icon-refresh text-danger p-l-5" data-toggle="tooltip"
                                    title="<?php echo app('translator')->get('lang.recurring_task'); ?>"></span>
                            <?php endif; ?>
                        </a>
                    <?php endif; ?>
                </td>
                <?php if(config('visibility.tasks_col_project')): ?>
                    <td class="tasks_col_project">
                        <span class="x-strike-through"><a title=""
                                href="<?php echo e(url('/projects/' . $task->project_id)); ?>"><?php echo e(str_limit($task->project_title ?? '---', 18)); ?></a></span>
                    </td>
                <?php endif; ?>
                <td>
                    <span class="x-strike-through"><?php echo e(str_limit($task->name ?? '---', 12)); ?></span>
                </td>
                <?php if(config('visibility.tasks_col_milestone')): ?>
                    <td class="tasks_col_milestone">
                        <span class="x-strike-through"><?php echo e(str_limit($task->milestone_title ?? '---', 12)); ?></span>
                    </td>
                <?php endif; ?>
                <?php if(config('visibility.tasks_col_date')): ?>
                    <td class="tasks_col_created"><?php echo e(runtimeDate($task->phase_start_date)); ?></td>
                <?php endif; ?>
                <td class="tasks_col_start_date"><?php echo e(runtimeDate($task->phase_start_date)); ?></td>
                
                <td class="tasks_col_deadline"><?php echo e(runtimeDate($task->phase_end_date)); ?></td>

                <?php if(config('visibility.tasks_col_assigned')): ?>
                    <?php
                        $phaseId = session('selected_phase_id');
                        $phaseUser = DB::table('task_phases')
                            ->join('task_phase_users', 'task_phase_users.task_phase_id', '=', 'task_phases.id')
                            ->join('users', 'users.id', '=', 'task_phase_users.user_id')
                            ->where('phase_id', $phaseId)
                            ->where('phase_task_id', $task->task_id)
                            ->first();

                    ?>

                    <td class="tasks_col_assigned" id="tasks_col_assigned_<?php echo e($task->task_id); ?>">
                        <!--assigned users-->
                        <?php if($phaseUser): ?>
                            <span class="ml-2"><?php echo e($phaseUser->first_name); ?></span>
                        <?php else: ?>
                            <span>---</span>
                        <?php endif; ?>
                    </td>
                <?php endif; ?>
                <?php if(config('visibility.tasks_col_all_time')): ?>
                    <td class="tasks_col_all_time">
                        <span class="x-timer-time"
                            id="task_timer_all_table_<?php echo e($task->task_id); ?>"><?php echo e(runtimeSecondsHumanReadable($task->sum_all_time, true)); ?></span>
                    </td>
                <?php endif; ?>
                
                <?php if(config('visibility.tasks_col_priority')): ?>
                    <td class="tasks_col_priority">
                        <span
                            class="label <?php echo e(runtimeTaskPriorityColors($task->task_priority, 'label')); ?>"><?php echo e(runtimeLang($task->task_priority)); ?></span>
                    </td>
                <?php endif; ?>
                <?php if(config('visibility.tasks_col_tags')): ?>
                    <td class="tasks_col_tags">
                        <!--tag-->
                        <?php if(count($task->tags ?? []) > 0): ?>
                            <?php $__currentLoopData = $task->tags->take(2); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tag): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <span class="label label-outline-default"><?php echo e(str_limit($tag->tag_title, 15)); ?></span>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php else: ?>
                            <span>---</span>
                        <?php endif; ?>
                        <!--/#tag-->

                        <!--more tags (greater than tags->take(x) number above -->
                        <?php if(count($task->tags ?? []) > 1): ?>
                            <?php $tags = $task->tags; ?>
                            <?php echo $__env->make('misc.more-tags', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                        <?php endif; ?>
                        <!--more tags-->
                    </td>
                <?php endif; ?>
                <td class="tasks_col_status">
                    <span class="label label-<?php echo e($task->color); ?>"><?php echo e(runtimeLang($task->phasestatus_title)); ?></span>
                    <!--archived-->
                    <?php if($task->task_active_state == 'archived' && runtimeArchivingOptions()): ?>
                        <span class="label label-icons label-icons-default" data-toggle="tooltip" data-placement="top"
                            title="<?php echo app('translator')->get('lang.archived'); ?>"><i class="ti-archive"></i></span>
                    <?php endif; ?>
                </td>


                <td class="tasks_col_action actions_column">
                    <!--action buttons-->
                    <span class="list-table-action dropdown font-size-inherit">
                        <!--delete-->
                        <?php if($task->permission_delete_task): ?>
                            <button type="button" title="<?php echo e(cleanLang(__('lang.delete'))); ?>"
                                class="data-toggle-action-tooltip btn btn-outline-danger btn-circle btn-sm confirm-action-danger"
                                data-confirm-title="<?php echo e(cleanLang(__('lang.delete_item'))); ?>"
                                data-confirm-text="<?php echo e(cleanLang(__('lang.are_you_sure'))); ?>" data-ajax-type="DELETE"
                                data-url="<?php echo e(url('/')); ?>/tasks/<?php echo e($task->task_id); ?>">
                                <i class="sl-icon-trash"></i>
                            </button>
                        <?php else: ?>
                            <!--optionally show disabled button?-->
                            <span
                                class="btn btn-outline-default btn-circle btn-sm disabled <?php echo e(runtimePlaceholdeActionsButtons()); ?>"
                                data-toggle="tooltip" title="<?php echo e(cleanLang(__('lang.actions_not_available'))); ?>">
                                <i class="sl-icon-trash"></i>
                            </span>
                        <?php endif; ?>

                        <!--edit-->
                        <?php if(config('visibility.action_buttons_edit')): ?>
                            <button type="button" title="<?php echo e(cleanLang(__('lang.edit'))); ?>"
                                class="data-toggle-action-tooltip btn btn-outline-success btn-circle btn-sm edit-add-modal-button js-ajax-ux-request reset-target-modal-form"
                                data-toggle="modal" data-target="#commonModal"
                                data-url="<?php echo e(urlResource('/tasks/' . $task->task_id . '/edit')); ?>"
                                data-loading-target="commonModalBody"
                                data-modal-title="<?php echo e(cleanLang(__('lang.edit_task'))); ?>">
                                <i class="sl-icon-note"></i>
                            </button>
                        <?php endif; ?>
                    </span>

                    <!--more button (team)-->
                    <?php if(auth()->user()->is_team && $task->permission_super_user): ?>
                        <span class="list-table-action dropdown  font-size-inherit">
                            <button type="button" id="listTableAction" data-toggle="dropdown" aria-haspopup="true"
                                aria-expanded="false" title="<?php echo e(cleanLang(__('lang.more'))); ?>"
                                class="data-toggle-action-tooltip btn btn-outline-default-light btn-circle btn-sm">
                                <i class="ti-more"></i>
                            </button>
                            <div class="dropdown-menu" aria-labelledby="listTableAction">

                                <!--clone task (team only)-->
                                <?php if(auth()->user()->is_team && $task->permission_edit_task): ?>
                                    <a class="dropdown-item edit-add-modal-button js-ajax-ux-request reset-target-modal-form"
                                        data-toggle="modal" data-target="#commonModal"
                                        data-modal-title="<?php echo app('translator')->get('lang.clone_task'); ?>"
                                        data-url="<?php echo e(urlResource('/tasks/' . $task->task_id . '/clone')); ?>"
                                        data-action-url="<?php echo e(urlResource('/tasks/' . $task->task_id . '/clone')); ?>"
                                        data-modal-size="modal-sm" data-loading-target="commonModalBody"
                                        data-action-method="POST" aria-expanded="false">
                                        <?php echo app('translator')->get('lang.clone_task'); ?>
                                    </a>
                                <?php endif; ?>

                                <!--record time-->
                                <?php if($task->assigned_to_me): ?>
                                    <a class="dropdown-item edit-add-modal-button js-ajax-ux-request reset-target-modal-form"
                                        data-confirm-title="<?php echo e(cleanLang(__('lang.archive_task'))); ?>"
                                        data-toggle="modal" data-target="#commonModal"
                                        data-modal-title="<?php echo app('translator')->get('lang.record_your_work_time'); ?>"
                                        data-url="<?php echo e(url('/timesheets/create?task_id=' . $task->task_id)); ?>"
                                        data-action-url="<?php echo e(urlResource('/timesheets')); ?>" data-modal-size="modal-sm"
                                        data-loading-target="commonModalBody" data-action-method="POST"
                                        aria-expanded="false">
                                        <?php echo e(cleanLang(__('lang.record_time'))); ?>

                                    </a>
                                <?php endif; ?>
                                <!--stop all timers-->
                                <a class="dropdown-item confirm-action-danger"
                                    data-confirm-title="<?php echo e(cleanLang(__('lang.stop_all_timers'))); ?>"
                                    data-confirm-text="<?php echo e(cleanLang(__('lang.are_you_sure'))); ?>" data-ajax-type="PUT"
                                    data-url="<?php echo e(url('/')); ?>/tasks/timer/<?php echo e($task->task_id); ?>/stopall?source=list">
                                    <?php echo e(cleanLang(__('lang.stop_all_timers'))); ?>

                                </a>

                                <?php if(auth()->user()->is_team && $task->permission_edit_task): ?>
                                    <!--recurring settings-->
                                    <a class="dropdown-item edit-add-modal-button js-ajax-ux-request reset-target-modal-form"
                                        href="javascript:void(0)" data-toggle="modal" data-target="#commonModal"
                                        data-url="<?php echo e(urlResource('/tasks/' . $task->task_id . '/recurring-settings?source=list')); ?>"
                                        data-loading-target="commonModalBody"
                                        data-modal-title="<?php echo e(cleanLang(__('lang.recurring_settings'))); ?>"
                                        data-action-url="<?php echo e(urlResource('/tasks/' . $task->task_id . '/recurring-settings?source=list')); ?>"
                                        data-action-method="POST"
                                        data-action-ajax-loading-target="tasks-td-container"><?php echo e(cleanLang(__('lang.recurring_settings'))); ?></a>
                                    <!--stop recurring -->
                                    <?php if($task->task_recurring == 'yes'): ?>
                                        <a class="dropdown-item confirm-action-info" href="javascript:void(0)"
                                            data-confirm-title="<?php echo e(cleanLang(__('lang.stop_recurring'))); ?>"
                                            data-confirm-text="<?php echo e(cleanLang(__('lang.are_you_sure'))); ?>"
                                            data-url="<?php echo e(urlResource('/tasks/' . $task->task_id . '/stop-recurring?source=list')); ?>">
                                            <?php echo e(cleanLang(__('lang.stop_recurring'))); ?></a>
                                    <?php endif; ?>
                                <?php endif; ?>

                                <!--archive-->
                                <?php if($task->task_active_state == 'active' && runtimeArchivingOptions()): ?>
                                    <a class="dropdown-item confirm-action-info"
                                        data-confirm-title="<?php echo e(cleanLang(__('lang.archive_task'))); ?>"
                                        data-confirm-text="<?php echo e(cleanLang(__('lang.are_you_sure'))); ?>"
                                        data-ajax-type="PUT"
                                        data-url="<?php echo e(urlResource('/tasks/' . $task->task_id . '/archive')); ?>">
                                        <?php echo e(cleanLang(__('lang.archive'))); ?>

                                    </a>
                                <?php endif; ?>
                                <!--activate-->
                                <?php if($task->task_active_state == 'archived' && runtimeArchivingOptions()): ?>
                                    <a class="dropdown-item confirm-action-info"
                                        data-confirm-title="<?php echo e(cleanLang(__('lang.restore_task'))); ?>"
                                        data-confirm-text="<?php echo e(cleanLang(__('lang.are_you_sure'))); ?>"
                                        data-ajax-type="PUT"
                                        data-url="<?php echo e(urlResource('/tasks/' . $task->task_id . '/activate')); ?>">
                                        <?php echo e(cleanLang(__('lang.restore'))); ?>

                                    </a>
                                <?php endif; ?>

                            </div>
                        </span>
                    <?php endif; ?>

                    <!--pin-->
                    <span class="list-table-action">
                        <a href="javascript:void(0);" title="<?php echo e(cleanLang(__('lang.pinning'))); ?>"
                            data-parent="task_<?php echo e($task->task_id); ?>"
                            data-url="<?php echo e(url('/tasks/' . $task->task_id . '/pinning')); ?>"
                            class="data-toggle-action-tooltip btn btn-outline-default-light btn-circle btn-sm opacity-4 js-toggle-pinning">
                            <i class="ti-pin2"></i>
                        </a>
                    </span>
                </td>
            </tr>

            
            <?php if(isset($task->subtasks) && count($task->subtasks) > 0): ?>
                <?php $__currentLoopData = $task->subtasks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $subtask): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <tr class="subtask-row subtask-<?php echo e($task->task_id); ?>" 
                        style="display: none; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); 
                               border-left: 3px solid #007bff; transition: all 0.4s ease;">
                        <td class="tasks_col_title td-edge" style="padding-left: 60px; position: relative;">
                            
                            <div class="subtask-connector" style="position: absolute; left: 20px; top: 0; bottom: 0; width: 2px; background: #007bff; opacity: 0.3;"></div>
                            <div class="subtask-connector-horizontal" style="position: absolute; left: 20px; top: 50%; width: 25px; height: 2px; background: #007bff; opacity: 0.3;"></div>
                            
                            <span class="task_border td-edge-border bg-info" style="border-radius: 50%; width: 8px; height: 8px;"></span>
                            <div class="subtask-content" style="display: inline-block; margin-left: 10px;">
                                <div class="subtask-title" style="font-weight: 500; color: #495057; font-size: 13px;">
                                    <i class="fas fa-tasks mr-2" style="color: #007bff; font-size: 11px;"></i>
                                    <?php echo e(str_limit($subtask->subtask_title ?? '---', 35)); ?>

                                </div>
                                <?php if($subtask->subtask_description): ?>
                                    <div class="subtask-description" style="font-size: 11px; color: #6c757d; margin-top: 2px;">
                                        <?php echo e(str_limit($subtask->subtask_description, 50)); ?>

                                    </div>
                                <?php endif; ?>
                            </div>
                        </td>
                        <?php if(config('visibility.tasks_col_project')): ?>
                            <td class="tasks_col_project">
                                <span class="text-muted" style="font-size: 12px;"><?php echo e(str_limit($task->project_title ?? '---', 18)); ?></span>
                            </td>
                        <?php endif; ?>
                        <td>
                            <span class="text-muted" style="font-size: 12px;"><?php echo e(str_limit($task->name ?? '---', 12)); ?></span>
                        </td>
                        <?php if(config('visibility.tasks_col_milestone')): ?>
                            <td class="tasks_col_milestone">
                                <span class="text-muted" style="font-size: 12px;">---</span>
                            </td>
                        <?php endif; ?>
                        <?php if(config('visibility.tasks_col_date')): ?>
                            
                            <td class="tasks_col_created" style="font-size: 12px;">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-calendar-plus mr-1" style="color: #6c757d; font-size: 10px;"></i>
                                    <span class="text-muted">
                                        <?php echo e(runtimeDate($subtask->created_at ?? $task->task_date)); ?>

                                    </span>
                                </div>
                            </td>
                        <?php endif; ?>
                        
                        <td class="tasks_col_start_date" style="font-size: 12px;">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-play mr-1" style="color: #28a745; font-size: 10px;"></i>
                                <span style="color: #28a745; font-weight: 500;">
                                    <?php echo e(runtimeDate($subtask->subtask_start_date)); ?>

                                </span>
                            </div>
                        </td>
                        
                        <td class="tasks_col_deadline" style="font-size: 12px;">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-flag-checkered mr-1" style="color: #dc3545; font-size: 10px;"></i>
                                <span style="color: #dc3545; font-weight: 500;">
                                    <?php echo e(runtimeDate($subtask->subtask_end_date)); ?>

                                </span>
                            </div>
                        </td>

                        <?php if(config('visibility.tasks_col_assigned')): ?>
                            <td class="tasks_col_assigned">
                                <?php if(isset($subtask->assigned_user_id) && $subtask->assigned_user_id): ?>
                                    <?php
                                        $subtaskUser = DB::table('users')->where('id', $subtask->assigned_user_id)->first();
                                    ?>
                                    <?php if($subtaskUser): ?>
                                        <div class="d-flex align-items-center">
                                            <div class="user-avatar" style="width: 20px; height: 20px; border-radius: 50%; background: #007bff; color: white; font-size: 10px; display: flex; align-items: center; justify-content: center; margin-right: 5px;">
                                                <?php echo e(strtoupper(substr($subtaskUser->first_name, 0, 1))); ?>

                                            </div>
                                            <span style="font-size: 12px;"><?php echo e($subtaskUser->first_name); ?></span>
                                        </div>
                                    <?php else: ?>
                                        <span class="text-muted" style="font-size: 12px;">---</span>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <span class="text-muted" style="font-size: 12px;">---</span>
                                <?php endif; ?>
                            </td>
                        <?php endif; ?>
                        <?php if(config('visibility.tasks_col_all_time')): ?>
                            <td class="tasks_col_all_time">
                                <span class="text-muted" style="font-size: 12px;">---</span>
                            </td>
                        <?php endif; ?>
                        <?php if(config('visibility.tasks_col_priority')): ?>
                            <td class="tasks_col_priority">
                                <span class="badge badge-light" style="font-size: 10px;">Low</span>
                            </td>
                        <?php endif; ?>
                        <?php if(config('visibility.tasks_col_tags')): ?>
                            <td class="tasks_col_tags">
                                <span class="text-muted" style="font-size: 12px;">---</span>
                            </td>
                        <?php endif; ?>
                        <td class="tasks_col_status">
                            <?php if(isset($subtask->subtask_status_id)): ?>
                                <?php
                                    $subtaskStatus = DB::table('tasks_status')
                                        ->where('taskstatus_id', $subtask->subtask_status_id)
                                        ->first();
                                ?>
                                <?php if($subtaskStatus): ?>
                                    <span class="badge badge-success" style="font-size: 10px; padding: 4px 8px;">
                                        <?php echo e($subtaskStatus->taskstatus_title); ?>

                                    </span>
                                <?php else: ?>
                                    <span class="badge badge-warning" style="font-size: 10px; padding: 4px 8px;">Pending</span>
                                <?php endif; ?>
                            <?php else: ?>
                                <span class="badge badge-warning" style="font-size: 10px; padding: 4px 8px;">Pending</span>
                            <?php endif; ?>
                        </td>
                        <td class="tasks_col_action actions_column">
                            <!--subtask info only - no action buttons-->
                            <span class="text-muted d-flex align-items-center justify-content-center">
                                <?php if($subtask->subtask_description): ?>
                                    <i class="ti-info-alt" 
                                       data-toggle="tooltip" 
                                       data-placement="top"
                                       title="<?php echo e($subtask->subtask_description); ?>"
                                       style="font-size: 14px; color: #6c757d; cursor: help;"></i>
                                <?php else: ?>
                                    <span style="font-size: 12px; color: #adb5bd;">---</span>
                                <?php endif; ?>
                            </span>
                        </td>
                    </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php endif; ?>

        <?php endif; ?>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    <!--each row-->


<script>
function toggleTaskSubtasks(taskId) {
    const subtaskRows = document.querySelectorAll('.subtask-' + taskId);
    const chevron = document.getElementById('chevron-' + taskId);
    const button = chevron.closest('.subtask-toggle-btn');
    
    if (subtaskRows.length > 0) {
        const isHidden = subtaskRows[0].style.display === 'none';
        
        // Add loading state
        button.style.opacity = '0.6';
        button.style.pointerEvents = 'none';
        
        if (isHidden) {
            // Show subtasks with staggered animation
            subtaskRows.forEach((row, index) => {
                setTimeout(() => {
                    row.style.display = 'table-row';
                    row.style.opacity = '0';
                    row.style.transform = 'translateX(-20px)';
                    
                    setTimeout(() => {
                        row.style.transition = 'all 0.4s ease';
                        row.style.opacity = '1';
                        row.style.transform = 'translateX(0)';
                    }, 50);
                }, index * 100);
            });
            
            // Rotate chevron
            chevron.style.transform = 'rotate(90deg)';
            chevron.style.color = '#28a745';
            button.setAttribute('data-original-title', 'Hide subtasks');
            
        } else {
            // Hide subtasks with reverse animation
            subtaskRows.forEach((row, index) => {
                setTimeout(() => {
                    row.style.opacity = '0';
                    row.style.transform = 'translateX(-20px)';
                    
                    setTimeout(() => {
                        row.style.display = 'none';
                    }, 300);
                }, index * 50);
            });
            
            // Reset chevron
            chevron.style.transform = 'rotate(0deg)';
            chevron.style.color = '#007bff';
            button.setAttribute('data-original-title', 'Show subtasks');
        }
        
        // Remove loading state
        setTimeout(() => {
            button.style.opacity = '1';
            button.style.pointerEvents = 'auto';
        }, 600);
    }
}

// Enhanced CSS animations and hover effects
document.addEventListener('DOMContentLoaded', function() {
    const style = document.createElement('style');
    style.textContent = `
        .subtask-toggle-btn {
            transition: all 0.3s ease !important;
        }
        .subtask-toggle-btn:hover {
            background: rgba(0, 123, 255, 0.1) !important;
            transform: scale(1.05);
        }
        .subtask-chevron {
            transition: all 0.3s ease !important;
        }
        .subtask-row:hover {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%) !important;
            transform: translateX(5px);
            box-shadow: 0 2px 8px rgba(0, 123, 255, 0.15);
        }
        .subtask-content {
            transition: all 0.2s ease;
        }
        .subtask-row:hover .subtask-content {
            transform: translateX(3px);
        }
        .user-avatar {
            transition: all 0.2s ease;
        }
        .subtask-row:hover .user-avatar {
            transform: scale(1.1);
        }
        .subtask-actions button {
            transition: all 0.2s ease;
            opacity: 0.7;
        }
        .subtask-row:hover .subtask-actions button {
            opacity: 1;
        }
        .main-task-row {
            transition: all 0.2s ease;
        }
        .main-task-row:hover {
            background-color: rgba(0, 123, 255, 0.02);
        }
    `;
    document.head.appendChild(style);
    
    // Initialize tooltips
    $('[data-toggle="tooltip"]').tooltip();
});
</script>
<?php /**PATH C:\Content\Laravel\CRM-Grow\application\resources\views/pages/tasks/components/table/ajax.blade.php ENDPATH**/ ?>