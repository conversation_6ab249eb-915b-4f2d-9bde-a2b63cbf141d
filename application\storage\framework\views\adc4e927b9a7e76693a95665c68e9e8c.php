<div class="row">
    <div class="col-lg-12">

        <?php if(config('visibility.task_modal_project_option')): ?>
            <!--project-->
            <div class="form-group row">
                <label
                    class="col-sm-12 col-lg-3 text-left control-label col-form-label required"><?php echo e(cleanLang(__('lang.project'))); ?>*</label>
                <div class="col-sm-12 col-lg-9">
                    <select name="task_projectid" id="task_projectid"
                        class="projects_assigned_toggle projects_assigned_client_toggle form-control form-control-sm js-select2-basic-search-modal select2-hidden-accessible"
                        data-assigned-dropdown="assigned" data-client-assigned-dropdown="assigned-client"
                        data-ajax--url="<?php echo e(url('/')); ?>/feed/projects?ref=general"></select>
                </div>
            </div>
        <?php endif; ?>

        <!--title-->
        <div class="form-group row">
            <label
                class="col-sm-12 col-lg-3 text-left control-label col-form-label required"><?php echo e(cleanLang(__('lang.title'))); ?>*</label>
            <div class="col-sm-12 col-lg-9">
                <input type="text" class="form-control form-control-sm" id="task_title" name="task_title"
                    placeholder="">
            </div>
        </div>



        <?php if(config('visibility.task_modal_milestone_option')): ?>
            <div class="form-group row">
                <label
                    class="col-sm-12 col-lg-3 text-left control-label col-form-label required"><?php echo e(cleanLang(__('lang.milestone'))); ?>*</label>
                <div class="col-sm-12 col-lg-9">
                    <select name="task_milestoneid" id="task_milestoneid"
                        class="select2-basic form-control form-control-sm">
                        <?php if(isset($milestones)): ?>
                            <?php $__currentLoopData = $milestones; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $milestone): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($milestone->milestone_id); ?>">
                                    <?php echo e(runtimeLang($milestone->milestone_title, 'task_milestone')); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php endif; ?>
                        <!--/#users list-->
                    </select>
                </div>
            </div>
        <?php endif; ?>

        <?php
            if (isSuperAdmin()) {
                //Superadmin lai sab workspace add garna dine
                $workspaces = App\Models\Tasks\Workspace::all();
            } else {
                //Normal user lai afulai assign vako user lai matra assign garna dine.
                $workspaces = myAssigendWorkspaces();
            }
        ?>
        <!--Task bata nei workspace add garnu-->
        

        <script>
            $(document).ready(function() {
                $('#assign_to_workspace').on('change', function() {
                    if ($(this).is(':checked')) {
                        $('#workspace_select_container').show();
                    } else {
                        $('#workspace_select_container').hide();
                        //populate the default statuses to task_status select
                        var defaultStatuses = <?php echo json_encode(config('task_statuses'), 15, 512) ?>;
                        $('#task_status').empty();
                        $.each(defaultStatuses, function(index, status) {
                            $('#task_status').append('<option value="' + status.taskstatus_id + '">' +
                                status.taskstatus_title + '</option>');
                        });
                    }
                });

                // $('#workspace_id').on('change', function() {
                //     var workspaceId = $(this).val();
                //     //send ajax request to get the statuses of the workspace
                //     var url = '/tasks/getworspaceTasksByStatus?workspace_id=' + workspaceId;
                //     //ajax req
                //     $.ajax({
                //         url: url,
                //         type: 'GET',
                //         data: {
                //             workspace_id: workspaceId
                //         },
                //         success: function(response) {
                //             var statuses = response;
                //             $('#task_status').empty();
                //             $.each(statuses, function(index, status) {
                //                 $('#task_status').append('<option value="' + status
                //                     .taskstatus_id + '">' +
                //                     status.taskstatus_title + '</option>');
                //             });
                //         }
                //     });
                // });
            });
        </script>


        <!--Currently ma workspace ma xu vane matra dekhaune yo..-->
        <?php if(session('workspace_id') != null): ?>
            <div class="form-group row">
                <label class="col-sm-12 col-lg-3 text-left control-label col-form-label"
                    style="font-weight: bold; font-size: 1.2em; color: #333;">
                    <i class="fas fa-cogs" style="color: #007bff;"></i> Add To Current Workspace?
                </label>
                <div class="col-sm-12 col-lg-9">
                    <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input" id="is_workspace" name="is_workspace"
                            value="1">
                        <label class="custom-control-label" for="is_workspace"
                            style="font-size: 1.1em; font-weight: bold; color: #007bff;">
                            Yes, Add to Workspace
                        </label>
                    </div>
                    <small class="form-text text-muted" style="font-size: 0.9em; color: #6c757d;">If You don't specify
                        the
                        workspace
                        task will be created as normal without a workspace.</small>
                </div>
            </div>
        <?php endif; ?>

        <?php
            $session = session('workspace_id');
            $workspaceStatuses = null;
            if ($session) {
                $workspace = App\Models\Tasks\Workspace::find($session);
                $workspaceStatuses = $workspace->taskStatuses;
            }
        ?>

        <script>
            $(document).ready(function() {
                $('#is_workspace').on('change', function() {
                    if ($(this).is(':checked')) {
                        //populate the $workspaceStatuses to task_status select
                        var workspaceStatuses = <?php echo json_encode($workspaceStatuses, 15, 512) ?>;
                        if (workspaceStatuses.length == 0) {
                            return;
                        }
                        $('#task_status').empty();
                        $.each(workspaceStatuses, function(index, status) {
                            $('#task_status').append('<option value="' + status.taskstatus_id + '">' +
                                status
                                .taskstatus_title + '</option>');
                        });
                    } else {
                        //populate the default statuses to task_status select
                        var defaultStatuses = <?php echo json_encode(config('task_statuses'), 15, 512) ?>;
                        $('#task_status').empty();
                        $.each(defaultStatuses, function(index, status) {
                            $('#task_status').append('<option value="' + status.taskstatus_id + '">' +
                                status.taskstatus_title + '</option>');
                        });
                    }
                });
            });
        </script>



        <?php if(config('visibility.tasks_standard_features')): ?>
            <div class="form-group row">
                <label class="col-sm-12 col-lg-3 text-left control-label col-form-label">Reported Date</label>
                <div class="col-sm-12 col-lg-9">
                    <input type="date" class="form-control form-control-sm " name="task_reported_date"
                        autocomplete="off" placeholder="">
                    <input class="mysql-date" type="hidden" name="task_reported_due" id="reported_date_due">
                </div>
            </div>
            
            <div class="form-group row">
                <label class="col-sm-12 col-lg-3 text-left control-label col-form-label">Start Date</label>
                <div class="col-sm-12 col-lg-9">
                    <input type="date" class="form-control form-control-sm " name="task_started_date"
                        id="start_date_due" autocomplete="off" placeholder="">
                    <input class="mysql-date" type="hidden" name="task_started_due" id="start_date_due_hidden">
                </div>
            </div>
            
            <div class="form-group row">
                <label
                    class="col-sm-12 col-lg-3 text-left control-label col-form-label"><?php echo e(cleanLang(__('lang.target_date'))); ?></label>
                <div class="col-sm-12 col-lg-9">
                    <input type="date" class="form-control form-control-sm " name="task_target_due"
                        id="task_date_duee" autocomplete="off" placeholder="">
                    <input class="mysql-date" type="hidden" name="task_date_due" id="task_date_due_hidden">
                </div>
            </div>
            <!--task status-->
            






            <!--task priority-->
            <div class="form-group row">
                <label
                    class="col-sm-12 col-lg-3 text-left control-label col-form-label required"><?php echo e(cleanLang(__('lang.priority'))); ?>*</label>
                <div class="col-sm-12 col-lg-9">
                    <select class="select2-basic form-control form-control-sm" id="task_priority" name="task_priority">
                        <?php $__currentLoopData = $priorities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $priority): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($priority->taskpriority_id); ?>"><?php echo e($priority->taskpriority_title); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>
            </div>

            <!--Adding the modules from Task itself-->
            <?php
                $modules = \App\Models\Module::where('is_parent', 1)->get();
                $allModules = \App\Models\Module::all();
                $phases = \App\Models\Phase::all();
            ?>




            <div class="client-selector">
                <div class="moduleSubModule">
                    <!--Project Modules-->
                    <div class="form-group row d-none">
                        <label
                            class="col-sm-12 col-lg-3 text-left control-label col-form-label required">Modules*</label>
                        <div class="col-sm-12 col-lg-9">
                            <select class="select2-basic form-control form-control-sm d-none " id="task_module"
                                name="task_module_none">
                                <option value=""></option>
                                <?php $__currentLoopData = $modules; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $module): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($module->id); ?>"><?php echo e($module->name); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label
                            class="col-sm-12 col-lg-3 text-left control-label col-form-label required">Modules</label>
                        <div class="col-sm-12 col-lg-9">
                            <select class="select2 form-control " id="task_module2" name="task_module">
                                <option value="">Select Module</option>
                                <?php $__currentLoopData = $modules; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $module): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($module->id); ?>"><?php echo e($module->name); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                    </div>

                    <!-- Sub Modules-->
                    <div class="form-group row SubModule" style="display: none">
                        <label class="col-sm-12 col-lg-3 text-left control-label col-form-label required">Sub
                            Modules</label>
                        <div class="col-sm-12 col-lg-9">
                            <select class="select2-basic form-control form-control-sm " id="sub_module"
                                name="task_sub_module">
                                <option value="">Select Module</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!--new Module-->
                <div class="client-selector-container hidden" id="client-new-container">
                    <div class="form-group row">
                        <label class="col-sm-12 col-lg-4 text-left control-label col-form-label required">New Module
                            Name*</label>
                        <div class="col-sm-12 col-lg-8">
                            <input type="text" class="form-control form-control-sm" id="module_name"
                                name="module_name">
                        </div>
                    </div>
                    <!-- Description-->
                    <div class="form-group row">
                        <label
                            class="col-sm-12 col-lg-4 text-left control-label col-form-label required">Description</label>
                        <div class="col-sm-12 col-lg-8">
                            <input type="text" class="form-control form-control-sm" id="module_description"
                                name="module_description" placeholder="short Description">
                        </div>
                    </div>

                    <!-- Status-->
                    <div class="form-group row">
                        <label
                            class="col-sm-12 col-lg-4 text-left control-label col-form-label required"><?php echo e(cleanLang(__('lang.status'))); ?>*</label>
                        <div class="col-sm-12 col-lg-8">
                            <select class="select2-basic form-control form-control-sm select2-preselected"
                                id="status_id" name="status" data-preselected="<?php echo e('active'); ?>">
                                <option value="active"><?php echo e(cleanLang(__('lang.active'))); ?></option>
                                <option value="inactive">Inactive</option>
                            </select>
                        </div>
                    </div>

                    
                    
                    <input type="hidden" id="task_dependencies_data" name="task_dependencies_data">

                    <!-- Parent Module -->
                    <div class="form-group row">
                        <label class="col-sm-12 col-lg-4 text-left control-label col-form-label required">Parent
                            Module:</label>
                        <div class="col-sm-12 col-lg-8">
                            <select class="select2-basic form-control form-control-sm" id="parent_id"
                                name="parent_id">
                                <option value="">Parent Module</option>
                                <?php $__currentLoopData = $allModules; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $module): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($module->id); ?>"><?php echo e(runtimeLang($module->name)); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                    </div>
                    <!-- Save Button-->
                    <span class="btn btn-primary btn-sm" onclick="moduleSubmit(event)"> <i class="fas fa-save"></i>
                        Save
                    </span>
                </div>


                <!--option buttons-->
                <div class="client-selector-links">
                    <a href="javascript:void(0)" class="client-type-selector" data-type="new"
                        data-target-container="client-new-container">New Modules</a> |
                    <a href="javascript:void(0)" class="client-type-selector active" data-type="existing"
                        data-target-container="client-existing-container">Existing Modules</a>
                </div>

                <!--client type indicator-->
                <input type="hidden" name="client-selection-type" id="client-selection-type" value="existing">
            </div>
            <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
            <link href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css" rel="stylesheet" />

            <script>
                $(document).ready(function() {
                    $('.client-type-selector').on('click', function() {
                        var type = $(this).data('type');
                        if (type == 'new') {
                            $('.moduleSubModule').hide();
                            $('#client-new-container').removeClass('hidden');
                        } else {
                            $('.moduleSubModule').show();
                            $('#client-new-container').addClass('hidden');
                        }
                        $('.client-type-selector').removeClass('active');
                        $(this).addClass('active');
                        $('#client-selection-type').val(type);
                    });


                });

                function moduleSubmit(event) {
                    event.preventDefault();
                    var moduleData = {
                        module_name: $('#module_name').val(),
                        module_description: $('#module_description').val(),
                        status: $('#status_id').val(),
                        is_parent: $('#is_parent').is(':checked'),
                        parent_id: $('#parent_id').val(),
                        task_projectid: $('#task_projectid').val()
                    };
                    if (!moduleData.module_name || !moduleData.task_projectid) {
                        toastr.error("Please fill in the required fields: Module Name and Project.");
                        return;
                    }

                    $.ajax({
                        url: "<?php echo e(url('tasks/newModule')); ?>",
                        type: 'POST',
                        data: moduleData,
                        headers: {
                            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                        },
                        success: function(response) {
                            //populate this response.module data to the select of id task_module and make that current module selected
                            $('#task_module2').append('<option value="' + response.module.id + '" selected>' + response
                                .module.name + '</option>');
                            toastr.success('Module Saved Successfully');
                            // $('#task_projectid').empty();
                        },
                        error: function(xhr, status, error) {
                            if (xhr.status === 400) {
                                var errorMessage = xhr.responseJSON && xhr.responseJSON.validation_error ?
                                    xhr.responseJSON.validation_error :
                                    'Error saving module. Please check Project and Module Name.';
                                toastr.error(errorMessage);
                            } else if (xhr.status === 500) {
                                var errorMessage = xhr.responseJSON && xhr.responseJSON.error ?
                                    xhr.responseJSON.error :
                                    'An unexpected error occurred during the module save process.';
                                toastr.error(errorMessage);
                            } else {
                                toastr.error('An unexpected error occurred. Please try again.');
                            }

                        }
                    });

                }
            </script>

            
            <?php echo $__env->make('pages.tasks.components.modals.task-phases', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

            
            <?php echo $__env->make('pages.tasks.components.modals.task-dependency', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <script>
                $(document).ready(function() {
                    $('#phase_id').select2({
                        placeholder: "Select a Phase",
                        allowClear: true
                    });
                });
            </script>

            

            
            
            



            <!--assigned [team users]-->
            <?php if(auth()->user()->role->role_assign_tasks == 'yes' && config('visibility.tasks_standard_features')): ?>
                <div class="form-group row">
                    <label
                        class="col-sm-12 col-lg-3 text-left control-label col-form-label"><?php echo e(cleanLang(__('lang.assign_users'))); ?>

                        <a class="align-middle font-16 toggle-collapse" href="#assigning_info" role="button"><i
                                class="ti-info-alt text-themecontrast"></i></a></label>
                    <div class="col-sm-12 col-lg-9">
                        <?php if(config('visibility.projects_assigned_users')): ?>
                            <select name="assigned" id="assigned"
                                class="form-control form-control-sm select2-basic select2-multiple select2-tags select2-hidden-accessible"
                                multiple="multiple" tabindex="-1" aria-hidden="true">
                                <?php $__currentLoopData = config('project.assigned'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php if($user->type == 'team'): ?>
                                        <option value="<?php echo e($user->id); ?>"><?php echo e($user->first_name); ?>

                                            <?php echo e($user->last_name); ?></option>
                                    <?php endif; ?>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        <?php else: ?>
                            <select name="assigned" id="assigned"
                                class="form-control form-control-sm select2-basic select2-multiple select2-tags select2-hidden-accessible"
                                multiple="multiple" tabindex="-1" aria-hidden="true" disabled>
                            </select>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="collapse" id="assigning_info">
                    <div class="alert alert-info">
                        <?php echo e(cleanLang(__('lang.assigning_users_to_a_task_info'))); ?>

                    </div>
                </div>
            <?php endif; ?>



            <!--assigned [client users]-->
            <?php if(auth()->user()->role->role_assign_tasks == 'yes' && config('visibility.tasks_standard_features')): ?>
                <div class="form-group row">
                    <label class="col-sm-12 col-lg-3 text-left control-label col-form-label"><?php echo app('translator')->get('lang.assign_client'); ?>
                        <a class="align-middle font-16 toggle-collapse" href="#assigning_client_info"
                            role="button"><i class="ti-info-alt text-themecontrast"></i></a></label>
                    <div class="col-sm-12 col-lg-9">
                        <?php if(config('visibility.projects_assigned_users')): ?>
                            <select name="assigned-client" id="assigned-client"
                                class="form-control form-control-sm select2-basic select2-multiple select2-tags select2-hidden-accessible"
                                multiple="multiple" tabindex="-1" aria-hidden="true">
                                <?php $__currentLoopData = config('project.client_users'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($user->id); ?>"><?php echo e($user->first_name); ?>

                                        <?php echo e($user->last_name); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        <?php else: ?>
                            <select name="assigned-client" id="assigned-client"
                                class="form-control form-control-sm select2-basic select2-multiple select2-tags select2-hidden-accessible"
                                multiple="multiple" tabindex="-1" aria-hidden="true" disabled>
                            </select>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="collapse" id="assigning_client_info">
                    <div class="alert alert-info">
                        <?php echo app('translator')->get('lang.assign_client_info'); ?>
                    </div>
                </div>
            <?php endif; ?>





            <div class="form-group row">
                <label class="col-sm-12 col-lg-4 text-left control-label col-form-label required">Parent
                    Module:</label>
                <div class="col-sm-12 col-lg-8">
                    <select class="select2-basic form-control form-control-sm" id="parent_id" name="parent_id">
                        <option value="">Parent Module</option>
                        <?php $__currentLoopData = $allModules; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $module): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($module->id); ?>"><?php echo e(runtimeLang($module->name)); ?></option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>
            </div>



            <!--CUSTOMER FIELDS [expanded]-->
            <?php if(config('system.settings_customfields_display_tasks') == 'expanded'): ?>
                <?php echo $__env->make('misc.customfields', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php endif; ?>
            <!--/#CUSTOMER FIELDS [expanded]-->

            <div class="line"></div>
            <!--spacer-->
            <div class="spacer row">
                <div class="col-sm-12 col-lg-8">
                    <span class="title"><?php echo e(cleanLang(__('lang.description'))); ?></span class="title">
                </div>
                <div class="col-sm-12 col-lg-4">
                    <div class="switch  text-right">
                        <label>
                            <input type="checkbox" name="show_more_settings_tasks" id="show_more_settings_tasks"
                                class="js-switch-toggle-hidden-content" data-target="task_description_section">
                            <span class="lever switch-col-light-blue"></span>
                        </label>
                    </div>
                </div>
            </div>
            <!--spacer-->

            <!--description-->
            <div class="hidden" id="task_description_section">
                <div class="form-group row">
                    <div class="col-sm-12">
                        <textarea id="project_description" name="task_description" class="tinymce-textarea"><?php echo e($task->task_description ?? ''); ?></textarea>
                    </div>
                </div>
            </div>



            <!--CUSTOMER FIELDS [collapsed]-->
            <?php if(config('system.settings_customfields_display_tasks') == 'toggled'): ?>
                <div class="spacer row">
                    <div class="col-sm-12 col-lg-8">
                        <span class="title"><?php echo e(cleanLang(__('lang.more_information'))); ?></span class="title">
                    </div>
                    <div class="col-sm-12 col-lg-4">
                        <div class="switch  text-right">
                            <label>
                                <input type="checkbox" name="add_client_option_other" id="add_client_option_other"
                                    class="js-switch-toggle-hidden-content"
                                    data-target="leads_custom_fields_collaped">
                                <span class="lever switch-col-light-blue"></span>
                            </label>
                        </div>
                    </div>
                </div>
                <div id="leads_custom_fields_collaped" class="hidden">
                    <?php if(config('app.application_demo_mode')): ?>
                        <!--DEMO INFO-->
                        <div class="alert alert-info">
                            <h5 class="text-info"><i class="sl-icon-info"></i> Demo Info</h5>
                            These are custom fields. You can change them or <a
                                href="<?php echo e(url('app/settings/customfields/projects')); ?>">create your own.</a>
                        </div>
                    <?php endif; ?>

                    <?php echo $__env->make('misc.customfields', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                </div>
            <?php endif; ?>
            <!--/#CUSTOMER FIELDS [collapsed]-->

            <?php if(config('visibility.task_modal_additional_options')): ?>
                <!--spacer-->
                <div class="spacer row">
                    <div class="col-sm-12 col-lg-8">
                        <span class="title"><?php echo e(cleanLang(__('lang.options'))); ?></span class="title">
                    </div>
                    <div class="col-sm-12 col-lg-4">
                        <div class="switch  text-right">
                            <label>
                                <input type="checkbox" name="show_more_settings_tasks2"
                                    id="show_more_settings_tasks2" class="js-switch-toggle-hidden-content"
                                    data-target="additional_information_section">
                                <span class="lever switch-col-light-blue"></span>
                            </label>
                        </div>
                    </div>
                </div>
                <!--spacer-->
                <!--option toggle-->
                <div class="hidden" id="additional_information_section">

                    <!--due date-->

                    
                    <?php if(config('visibility.tasks_standard_features')): ?>
                        <div class="container mt-3">
                            <div class="row">
                                <!-- Working Days -->
                                <div class="col-12 col-md-6 mb-3 workingDays" style="display: none">
                                    <div class="card border-light shadow-sm">
                                        <div class="card-body text-center">
                                            <h5 class="card-title mb-2">Working Days</h5>
                                            <h4 class="text-primary" id="working-days-result" name="working-days">0
                                                Days
                                            </h4>
                                            <input type="hidden" name="working-days">

                                        </div>
                                    </div>
                                </div>

                                <!-- Working Hours -->
                                <div class="col-12 col-md-6 mb-3 workingHours" style="display: none">
                                    <div class="card border-light shadow-sm">
                                        <div class="card-body text-center">
                                            <h5 class="card-title mb-2">Working Hours</h5>
                                            <input type="number" class="form-control text-center"
                                                id="working-hours-input" name="working-hours" value="0"
                                                min="0">
                                            <small id="working-hours-result" class="form-text text-success">0
                                                Hrs</small>

                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
            <?php endif; ?>

            <!--tags-->
            <div class="form-group row">
                <label
                    class="col-sm-12 col-lg-3 text-left control-label col-form-label"><?php echo e(cleanLang(__('lang.tags'))); ?></label>
                <div class="col-sm-12 col-lg-9">
                    <select name="tags" id="tags"
                        class="form-control form-control-sm select2-multiple <?php echo e(runtimeAllowUserTags()); ?> select2-hidden-accessible"
                        multiple="multiple" tabindex="-1" aria-hidden="true">
                        <!--array of selected tags-->
                        <?php if(isset($page['section']) && $page['section'] == 'edit'): ?>
                            <?php $__currentLoopData = $lead->tags; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tag): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php $selected_tags[] = $tag->tag_title ; ?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php endif; ?>
                        <!--/#array of selected tags-->
                        <?php $__currentLoopData = $tags; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tag): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($tag->tag_title); ?>"
                                <?php echo e(runtimePreselectedInArray($tag->tag_title ?? '', $selected_tags ?? [])); ?>>
                                <?php echo e($tag->tag_title); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>
            </div>


            <!--[toggled] project options-->
            <div class="toggle_task_type add_task_toggle_container_project">
                <div class="form-group form-group-checkbox row">
                    <div class="col-12 p-t-5">
                        <div class="pull-left min-w-200">
                            <label><?php echo e(cleanLang(__('lang.visible_to_client'))); ?></label>
                        </div>
                        <div class="pull-left p-l-10">
                            <?php if(isset($page['section']) && $page['section'] == 'create'): ?>
                                <input type="checkbox" id="task_client_visibility" name="task_client_visibility"
                                    <?php echo e(runtimeTasksDefaults('task_client_visibility')); ?>

                                    class="filled-in chk-col-light-blue">
                            <?php endif; ?>
                            <?php if(isset($page['section']) && $page['section'] == 'edit'): ?>
                                <input type="checkbox" id="task_client_visibility" name="task_client_visibility"
                                    class="filled-in chk-col-light-blue">
                            <?php endif; ?>
                            <label for="task_client_visibility"></label>
                        </div>
                    </div>
                </div>
                <div class="form-group form-group-checkbox row">
                    <div class="col-12 p-t-5">
                        <div class="pull-left min-w-200">
                            <label><?php echo e(cleanLang(__('lang.billable'))); ?></label>
                        </div>
                        <div class="pull-left p-l-10">
                            <?php if(isset($page['section']) && $page['section'] == 'create'): ?>
                                <input type="checkbox" id="task_billable" name="task_billable"
                                    <?php echo e(runtimeTasksDefaults('task_billable')); ?> class="filled-in chk-col-light-blue">
                            <?php endif; ?>
                            <?php if(isset($page['section']) && $page['section'] == 'edit'): ?>
                                <input type="checkbox" id="task_billable" name="task_billable"
                                    class="filled-in chk-col-light-blue">
                            <?php endif; ?>
                            <label for="task_billable"></label>
                        </div>
                    </div>
                </div>
            </div>
    </div>
    <!--option toggle-->
    <?php endif; ?>


    <!--redirect to project-->
    <?php if(config('visibility.task_show_task_option')): ?>
        <div class="line"></div>
        <div class="form-group form-group-checkbox row">
            <div class="col-12 text-left p-t-5">
                <input type="checkbox" id="show_after_adding" name="show_after_adding"
                    class="filled-in chk-col-light-blue" checked="checked">
                <label for="show_after_adding"><?php echo e(cleanLang(__('lang.show_task_after_adding'))); ?></label>
            </div>
        </div>
    <?php endif; ?>

    <!--pass source-->
    <input type="hidden" name="source" value="<?php echo e(request('source')); ?>">
    <input type="hidden" name="ref" value="<?php echo e(request('ref')); ?>">

    <!--notes-->
    <div class="row">
        <div class="col-12">
            <div><small><strong>* <?php echo e(cleanLang(__('lang.required'))); ?></strong></small></div>
        </div>
    </div>
</div>
</div>


<script>
    // Task Module Fetching Logic here
    $(document).ready(function() {

        $('#task_module2').on('change', function() {
            var parentModuleId = $(this).val();
            $('#sub_module').empty();


            // var url = '/tasks/getModule?parent_module_id=' + parentModuleId + '&sub=true';
            $.ajax({
                url: "<?php echo e(url('/tasks/getModule/{parentModuleId}')); ?>",
                type: 'GET',
                data: {
                    parentModuleId: parentModuleId,
                    sub: true
                },
                type: 'GET',
                success: function(response) {
                    if (response && response.length > 0) {
                        $('.SubModule').show();
                        $('#sub_module').append(
                            '<option value="">Select Sub Module</option>');

                        $.each(response, function(index, module) {
                            $('#sub_module').append('<option value="' +
                                module.id + '">' + module.name + '</option>'
                            );
                        });
                    }
                },
                error: function(xhr, status, error) {
                    console.log('Error fetching modules: ' + error);
                }
            });

        });


        $('#task_projectid').on('change', function() {
            var projectId = $(this).val();

            $('#task_module2').empty();

            if (projectId) {
                $.ajax({
                    url: "<?php echo e(url('/tasks/getModule/{projectId}')); ?>",
                    type: 'GET',
                    data: {
                        project_id: projectId
                    },
                    success: function(response) {
                        if (response && response.length > 0) {
                            $('#task_module2').empty();

                            $('#task_module2').append(
                                '<option value="">Select Module</option>');
                            for (var i = 0; i < response.length; i++) {
                                $('#task_module2').append('<option value="' + response[i]
                                    .id + '">' + response[i].name + '</option>');
                            }
                            // $.each(response, function(index, module) {
                            //     $('#task_module').append('<option value="' + module
                            //         .id + '">' + module.name + '</option>');
                            // });
                        }
                    },
                    error: function(xhr, status, error) {
                        console.log('Error fetching modules: ' + error);
                    }
                });
            }
        });

    });







    // $(document).ready(function() {
    // Function to calculate working days between two dates
    function calculateWorkingDaysAndHours(startDate, endDate) {
        let start = new Date(startDate);
        let end = new Date(endDate);
        let workingDays = 0;
        let workingHours = 0;

        // Case 1: If start and end date are the same
        if (start.getTime() === end.getTime()) {
            let dayOfWeek = start.getDay();
            if (dayOfWeek !== 5 && dayOfWeek !== 6) { // Monday to Friday are working days
                workingDays = 0; // Same day => 0 working days
                workingHours = 9; // But 9 hours for same day
            }
        } else {
            // Case 2: Different start and end dates (looping through dates)
            let currentDate = new Date(start);
            while (currentDate < end) { // Change <= to < to exclude the end date
                let dayOfWeek = currentDate.getDay();
                if (dayOfWeek !== 5 && dayOfWeek !== 6) { // Skip weekends (Friday = 5, Saturday = 6)
                    workingDays++; // Count this day as a working day
                    workingHours += 9; // Each working day has 9 working hours
                }
                currentDate.setDate(currentDate.getDate() + 1); // Move to the next day
            }
        }

        return {
            workingDays,
            workingHours
        };
    }

    function updateWorkingDaysAndHours() {
        let startDate = $('#start_date_due').val();
        let endDate = $('#task_date_duee').val();

        if (startDate && endDate) {
            // Get working days and hours based on the new logic
            let {
                workingDays,
                workingHours
            } = calculateWorkingDaysAndHours(startDate, endDate);
            // Update the working days result
            $('#working-days-result').text(workingDays + ' Days');
            $('.workingDays').show();
            $('.workingHours').show();

            // Update the working days input field
            $('input[name="working-days"]').val(workingDays);

            // Update the working hours result
            $('#working-hours-input').val(workingHours); // Automatically set to the calculated value
            $('#working-hours-result').text(workingHours + ' Hrs');
        }
    }

    // Event listeners for changes in start or end date
    $('#start_date_due').on('change', function() {
        updateWorkingDaysAndHours(); // Recalculate working days and hours when start date changes
    });

    $('#task_date_duee').on('change', function() {
        updateWorkingDaysAndHours(); // Recalculate working days and hours when end date changes
    });

    // Allow the user to manually edit working hours
    $('#working-hours-input').on('input', function() {
        let manualWorkingHours = $(this).val();
        $('#working-hours-result').text(manualWorkingHours + ' Hrs'); // Update the result text
    });




    // });
</script>
<?php /**PATH C:\Content\Laravel\CRM-Grow\application\resources\views/pages/tasks/components/modals/add-edit-inc.blade.php ENDPATH**/ ?>