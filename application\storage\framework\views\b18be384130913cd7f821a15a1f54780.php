<?php
    $task_id = request('commentresource_id');
    // if current phase has pushed the task then show that phase only the task report..
    $isDataAvailable = DB::table('phase_logs')
        ->where('pushedbyphase_id', session('selected_phase_id'))
        ->where('pushed_task_id', $task_id)
        ->exists();

?>

<?php if($isDataAvailable || session('selected_phase_id') == 4): ?>
    <div class="card-tester-reports" id="card-tester-reports">
        <div class="x-heading" id="tester-reports-heading">
            <i class="mdi mdi-clipboard-check-outline"></i>Tester Reports
            <span class="x-heading-icon"> 
                <i class="ti-angle-down js-toggle-tester-reports"></i>
            </span>
        </div>
        <div class="x-content" id="tester-reports-content" style="display: none;">
            <!-- New Report Form -->
            <form id="new-tester-report-form" class="mb-3">
                <?php echo csrf_field(); ?>
                <!-- Report Type Selection -->
                <div class="form-group">
                    <select id="report-type" name="report_type" class="form-control form-control-sm select2-basic"
                        required>
                        <option value="">Select Report Type</option>
                        <option value="bug">Bug</option>
                        <option value="issue">Issue</option>
                        <option value="new_request">New Feature Request</option>
                    </select>
                </div>

                <div class="form-group">
                    <textarea class="form-control form-control-sm" id="report_description" name="report_description" rows="3"
                        placeholder="What did you find? Describe the issue, bug or feature request..." required></textarea>
                </div>

                <!-- Attachment Upload -->
                <div class="card-attachments" id="card-attachments-tester">
                    <div class="x-action"><a class="card_fileupload" id="js-card-toggle-fileupload-tester"
                            href="javascript:void(0)"><?php echo e(cleanLang(__('lang.add_attachment'))); ?></a></div>
                    <div class="hidden" id="card-fileupload-container-tester">
                        <!--dropzone-->
                        <div class="dropzone dz-clickable" id="card_fileupload_tester">
                            <div class="dz-default dz-message">
                                <i class="icon-Upload-toCloud"></i>
                                <span><?php echo e(cleanLang(__('lang.drag_drop_file'))); ?></span>
                            </div>
                        </div>
                    </div>
                </div>

                <button type="submit" class="btn btn-info btn-sm mt-2">Submit Report</button>
            </form>

            <!-- Reports List -->
            <div class="table-responsive" id="tester-reports-table-container" style="display: none;">
                <table class="table table-sm table-hover">
                    <thead>
                        <tr>
                            <th>Type</th>
                            <th>Description</th>
                            <th>Done</th>
                        </tr>
                    </thead>
                    <tbody id="tester-reports-container">
                        <!-- Reports will be loaded here -->
                    </tbody>
                </table>
            </div>
            <div id="no-reports-message" class="text-center p-3">
                <p class="text-muted">No reports found for this task.</p>
            </div>
        </div>
    </div>
<?php endif; ?>

<style>
    .checkbox-inline {
        position: relative;
        display: inline-block;
    }

    .checkbox-inline input[type="checkbox"] {
        position: absolute;
        opacity: 0;
        cursor: pointer;
        height: 0;
        width: 0;
    }

    .checkbox-inline label {
        position: relative;
        cursor: pointer;
        padding-left: 25px;
        margin-bottom: 0;
    }

    .checkbox-inline label:before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        width: 18px;
        height: 18px;
        border: 1px solid #ddd;
        background: #fff;
        border-radius: 3px;
    }

    .checkbox-inline input[type="checkbox"]:checked+label:after {
        content: '';
        position: absolute;
        left: 6px;
        top: 2px;
        width: 6px;
        height: 12px;
        border: solid #2196F3;
        border-width: 0 2px 2px 0;
        transform: rotate(45deg);
    }

    .tester-report-attachments {
        margin-top: 10px;
        padding-top: 5px;
    }

    .file-attachment-item {
        padding: 5px 0;
        border-bottom: 1px solid #f0f0f0;
    }

    .file-attachment-item:last-child {
        border-bottom: none;
    }

    .attachment-actions {
        margin-left: 10px;
    }

    /* Adjust image preview size */
    .attachment-preview {
        margin: 5px 0;
        text-align: center;
    }

    .attachment-preview img {
        max-height: 120px;
        max-width: 100%;
        border-radius: 4px;
        border: 1px solid #eee;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        object-fit: contain;
    }

    .x-heading-icon {
        float: right;
        cursor: pointer;
    }

    .js-toggle-tester-reports {
        transition: transform 0.3s ease;
    }

    .js-toggle-tester-reports.rotate {
        transform: rotate(180deg);
    }

    /* Add styling for completed reports */
    .completed-report {
        background-color: rgba(0, 128, 0, 0.05);
    }

    .completed-report td {
        opacity: 0.8;
    }

    /* Fix for double checkmarks */
    .checkbox-inline {
        position: relative;
        display: inline-block;
        margin-right: 10px;
        vertical-align: middle;
    }

    .checkbox-inline input[type="checkbox"] {
        position: absolute;
        opacity: 0;
        cursor: pointer;
        height: 0;
        width: 0;
    }

    .checkbox-inline label {
        position: relative;
        cursor: pointer;
        padding-left: 25px;
        margin-bottom: 0;
    }

    .checkbox-inline label:before {
        content: "";
        position: absolute;
        left: 0;
        top: 0;
        width: 18px;
        height: 18px;
        border: 1px solid #ddd;
        background-color: #fff;
        border-radius: 3px;
    }

    .checkbox-inline input[type="checkbox"]:checked+label:after {
        content: "✓";
        position: absolute;
        left: 4px;
        top: -1px;
        font-size: 14px;
        color: #2196F3;
    }

    /* Action column styling to match project style */
    .actions_column {
        white-space: nowrap;
    }

    .list-table-action {
        display: flex;
        align-items: center;
    }

    /* Image overlay styling */
    #image-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.85);
        z-index: 9999;
        display: flex;
        align-items: center;
        justify-content: center;
        display: none;
    }

    .image-overlay-content {
        background-color: #fff;
        border-radius: 5px;
        max-width: 90%;
        max-height: 90%;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
    }

    .image-overlay-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 15px;
        background-color: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
    }

    .image-filename {
        font-weight: bold;
        font-size: 16px;
    }

    .close-overlay {
        font-size: 24px;
        font-weight: bold;
        color: #000;
        text-decoration: none;
        cursor: pointer;
    }

    .close-overlay:hover {
        color: #dc3545;
    }

    .image-container {
        padding: 15px;
        overflow: auto;
        text-align: center;
        max-height: 70vh;
    }

    .image-container img {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
    }

    .image-overlay-footer {
        padding: 10px 15px;
        background-color: #f8f9fa;
        border-top: 1px solid #dee2e6;
        text-align: right;
    }
</style>

<script>
    $(document).ready(function() {
        // Initialize select2
        $('.select2-basic').select2();

        // Toggle file upload container
        $("#js-card-toggle-fileupload-tester").off('click').on('click', function() {
            $("#card-fileupload-container-tester").toggleClass('hidden');
        });

        // Remove any existing click handlers before adding a new one
        $(".js-toggle-tester-reports").off('click');

        // Toggle tester reports section
        $(".js-toggle-tester-reports").on('click', function() {
            $("#tester-reports-content").slideToggle(300);
            $(this).toggleClass('rotate');

            // Always load reports when the toggle button is clicked
            const taskId = '<?php echo e($task->task_id); ?>';
            loadTesterReports(taskId);
        });

        // Initialize dropzone for file uploads - but don't upload immediately
        var uploadedFiles = [];

        Dropzone.autoDiscover = false;
        var myDropzone = new Dropzone("#card_fileupload_tester", {
            url: "<?php echo e(url('/tasks/' . $task->task_id . '/tester-report/submit')); ?>",
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            autoProcessQueue: false,
            uploadMultiple: true,
            parallelUploads: 10,
            maxFiles: 10,
            maxFilesize: 10, // MB
            acceptedFiles: ".jpeg,.jpg,.png,.gif,.pdf,.doc,.docx,.xls,.xlsx,.zip,.txt",
            addRemoveLinks: true,
            init: function() {
                dzClosure = this;

                // Store dropzone instance for later use
                window.testerDropzone = this;
            }
        });

        // Handle form submission
        $('#new-tester-report-form').on('submit', function(e) {
            e.preventDefault();

            // Get form data
            const formData = new FormData(this);
            const taskId = '<?php echo e($task->task_id); ?>';

            // Check if report type is selected
            if (!formData.get('report_type')) {
                toastr.error('Please select a report type');
                return;
            }

            // If there are files in dropzone, process them
            if (myDropzone.files.length > 0) {
                // Add files to form data
                myDropzone.files.forEach(function(file, index) {
                    formData.append('attachments[]', file);
                });
            }

            // Submit the form with AJAX
            $.ajax({
                url: '/tasks/' + taskId + '/tester-report/submit',
                method: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    if (response.success) {
                        // Clear form
                        $('#new-tester-report-form')[0].reset();
                        $('#report-type').val('').trigger('change');

                        // Clear dropzone
                        myDropzone.removeAllFiles(true);

                        // Hide file upload container
                        $("#card-fileupload-container-tester").addClass('hidden');

                        // Show success message
                        toastr.success('Report submitted successfully');

                        // Reload the reports to get the updated list
                        loadTesterReports(taskId);
                    }
                },
                error: function(xhr) {
                    let errorMessage = 'Error submitting report';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    }
                    toastr.error(errorMessage);
                }
            });
        });

        // Format attachments from the response
        function formatAttachments(attachments) {
            if (!attachments || attachments.length === 0) {
                return '';
            }

            let html = '<div class="tester-report-attachments">';

            attachments.forEach(function(attachment) {
                // Extract filename from URL
                const url = attachment.url;
                const filename = url.substring(url.lastIndexOf('/') + 1);
                const uniqueid = url.split('/').slice(-2)[
                    0]; // Get the directory name which is the uniqueid

                html += `
                <div class="file-attachment-item" id="file-${uniqueid}">
                    <span>${filename}</span>
                    <span class="attachment-actions">
                        <a href="${url}" class="text-info" download>
                            Download
                        </a> | 
                        <a href="javascript:void(0)" class="text-danger delete-attachment" 
                           data-id="${uniqueid}" data-filename="${filename}">
                            Delete
                        </a>
                    </span>
                </div>`;

                // If it's an image, show preview
                if (isImageFile(filename)) {
                    html += `
                    <div class="attachment-preview">
                        <img src="${url}" alt="${filename}">
                    </div>`;
                }
            });

            html += '</div>';
            return html;
        }

        // Check if file is an image
        function isImageFile(filename) {
            const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
            const extension = filename.split('.').pop().toLowerCase();
            return imageExtensions.includes(extension);
        }

        // Remove any existing click handlers for completion checkboxes
        $(document).off('change', '.completion-checkbox');

        // Handle completion checkbox changes
        $(document).on('change', '.completion-checkbox', function() {
            const reportId = $(this).data('id');
            // Skip if this is a new report without an ID
            if (reportId === 'new') {
                toastr.warning('Cannot update status of a new report');
                $(this).prop('checked', false);
                return;
            }

            const isCompleted = $(this).prop('checked');
            const taskId = '<?php echo e($task->task_id); ?>';
            const checkbox = $(this);

            // Disable checkbox during request
            checkbox.prop('disabled', true);

            $.ajax({
                url: '/tasks/tester-report/toggle-completion',
                method: 'GET',
                data: {
                    completed: isCompleted,
                    taskId: taskId,
                    reportId: reportId,
                    _token: '<?php echo e(csrf_token()); ?>'
                },
                success: function(response) {
                    if (response.success) {
                        toastr.success('Report status updated');

                        // Update the row styling to indicate completion status
                        if (isCompleted) {
                            $(`#tester-report-${reportId}`).addClass('completed-report');
                        } else {
                            $(`#tester-report-${reportId}`).removeClass('completed-report');
                        }

                        // Reload all reports to ensure everything is up to date
                        loadTesterReports(taskId);
                    } else {
                        // Revert checkbox state on error
                        checkbox.prop('checked', !isCompleted);
                        toastr.error('Error updating report status');
                    }
                },
                error: function(xhr) {
                    console.error('Error updating report status:', xhr);
                    // Revert checkbox state on error
                    checkbox.prop('checked', !isCompleted);
                    toastr.error('Error updating report status');
                },
                complete: function() {
                    // Re-enable checkbox
                    checkbox.prop('disabled', false);
                }
            });
        });

        // Handle attachment deletion
        $(document).on('click', '.delete-attachment', function() {
            const uniqueid = $(this).data('id');
            const filename = $(this).data('filename');

            $.ajax({
                url: '/tasks/tester-report/attachment/' + uniqueid,
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    if (response.success) {
                        // Remove the attachment from the DOM
                        $(`#file-${uniqueid}`).fadeOut(300, function() {
                            $(this).remove();
                        });
                        toastr.success('Attachment deleted successfully');
                    }
                },
                error: function() {
                    toastr.error('Error deleting attachment');
                }
            });
        });

        // Function to render attachments
        function renderAttachments(attachments) {
            if (!attachments || attachments.length === 0) {
                return '';
            }

            let html = '<div class="tester-report-attachments">';

            attachments.forEach(function(attachment) {
                html += `
                <div class="file-attachment-item" id="file-${attachment.uniqueid}">
                    <span>${attachment.filename}</span>
                    <span class="attachment-actions">
                        <a href="/tasks/download-attachment/${attachment.uniqueid}" class="text-info" download>
                            Download
                        </a> | `;

                // If it's an image, add view option
                if (isImageFile(attachment.filename)) {
                    html += `
                        <a href="javascript:void(0)" class="text-primary view-image" 
                           data-url="/storage/files/${attachment.uniqueid}/${attachment.filename}" 
                           data-filename="${attachment.filename}">
                            View
                        </a> | `;
                }

                html += `
                        <a href="javascript:void(0)" class="text-danger delete-attachment" 
                           data-id="${attachment.uniqueid}" data-filename="${attachment.filename}">
                            Delete
                        </a>
                    </span>
                </div>`;
            });

            html += '</div>';
            return html;
        }

        // Function to load tester reports
        function loadTesterReports(taskId) {
            console.log('Loading reports for task:', taskId);
            // Show loading indicator
            $('#tester-reports-container').html(
                '<tr><td colspan="3" class="text-center"><i class="fa fa-spinner fa-spin"></i> Loading reports...</td></tr>'
            );

            return $.ajax({
                url: '/tasks/' + taskId + '/tester-report/list',
                method: 'GET',
                success: function(response) {
                    console.log('Reports response:', response);
                    // Clear container
                    $('#tester-reports-container').empty();

                    if (response.reports && response.reports.length > 0) {
                        // Show table and hide no reports message
                        $('#tester-reports-table-container').show();
                        $('#no-reports-message').hide();

                        // Add reports to container
                        response.reports.forEach(function(report) {
                            // Get report ID - check all possible properties
                            const reportId = report.report_id || report.id || 'new';

                            // Check if report is completed
                            const isCompleted = report.done === 1 || report.completed ===
                                true;

                            const typeClass = getTypeClass(report.type);

                            // Format attachments if they exist
                            let attachmentsHtml = '';
                            if (report.url) {
                                // If the report has a direct URL (single attachment)
                                const filename = report.url.substring(report.url
                                    .lastIndexOf('/') + 1);
                                const uniqueid = report.url.split('/').slice(-2)[0];

                                attachmentsHtml = `
                                <div class="file-attachment-item" id="file-${uniqueid}">
                                    <span>${filename}</span>
                                    <span class="attachment-actions">
                                        <a href="${report.url}" class="text-info" download>
                                            Download
                                        </a> | `;

                                // If it's an image, add view option
                                if (isImageFile(filename)) {
                                    attachmentsHtml += `
                                        <a href="javascript:void(0)" class="text-primary view-image" 
                                           data-url="${report.url}" data-filename="${filename}">
                                            View
                                        </a> | `;
                                }

                                attachmentsHtml += `
                                        <a href="javascript:void(0)" class="text-danger delete-attachment" 
                                           data-id="${uniqueid}" data-filename="${filename}">
                                            Delete
                                        </a>
                                    </span>
                                </div>`;
                            } else if (report.attachments && report.attachments.length >
                                0) {
                                // If the report has an attachments array
                                attachmentsHtml = renderAttachments(report.attachments);
                            }

                            const html = `
                            <tr id="tester-report-${reportId}" class="${isCompleted ? 'completed-report' : ''}">
                                <td><span class="badge ${typeClass}">${report.type}</span></td>
                                <td>
                                    <div>${report.description}</div>
                                    ${attachmentsHtml}
                                </td>
                                <td class="actions_column">
                                    <span class="list-table-action">
                                        <div class="checkbox-inline">
                                            <input type="checkbox" class="completion-checkbox" 
                                                id="completion-${reportId}" data-id="${reportId}"
                                                ${isCompleted ? 'checked' : ''}>
                                            <label for="completion-${reportId}"></label>
                                        </div>
                                        <button type="button" title=""
                                            class="data-toggle-action-tooltip btn btn-outline-danger btn-circle btn-sm confirm-action-danger delete-report"
                                            data-confirm-title="Delete Report" 
                                            data-confirm-text="Are you sure?" 
                                            data-id="${reportId}">
                                            <i class="sl-icon-trash"></i>
                                        </button>
                                    </span>
                                </td>
                            </tr>
                            `;

                            $('#tester-reports-container').append(html);
                        });

                        // If no reports at all, show no reports message
                        if ($('#tester-reports-container tr').length === 0) {
                            $('#tester-reports-table-container').hide();
                            $('#no-reports-message').show();
                        }
                    } else {
                        // Hide table and show no reports message
                        $('#tester-reports-table-container').hide();
                        $('#no-reports-message').show();
                    }
                },
                error: function(error) {
                    console.error('Error loading reports:', error);
                    toastr.error('Error loading reports');
                    $('#tester-reports-container').html(
                        '<tr><td colspan="3" class="text-center text-danger">Error loading reports. Please try again.</td></tr>'
                    );
                }
            });
        }

        // Helper function to get badge class based on report type
        function getTypeClass(type) {
            switch (type) {
                case 'bug':
                case 'Bug':
                    return 'badge-danger';
                case 'issue':
                case 'Issue':
                    return 'badge-warning';
                case 'new_request':
                case 'New Request':
                    return 'badge-info';
                default:
                    return 'badge-secondary';
            }
        }

        // Handle report deletion
        $(document).off('click', '.delete-report').on('click', '.delete-report', function() {
            const reportId = $(this).data('id');
            const taskId = '<?php echo e($task->task_id); ?>';

            if (confirm('Are you sure you want to delete this report?')) {
                $.ajax({
                    url: '/tasks/' + taskId + '/tester-report/' + reportId + '/delete',
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        if (response.success) {
                            // Remove the report from the DOM
                            $(`#tester-report-${reportId}`).slideUp(300, function() {
                                $(this).remove();

                                // If no reports left, show the no reports message
                                if ($('#tester-reports-container tr').length ===
                                    0) {
                                    $('#tester-reports-table-container').hide();
                                    $('#no-reports-message').show();
                                }
                            });

                            toastr.success('Report deleted successfully');
                        } else {
                            toastr.error('Error deleting report');
                        }
                    },
                    error: function(xhr) {
                        console.error('Error deleting report:', xhr);
                        let errorMessage = 'Error deleting report';
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMessage = xhr.responseJSON.message;
                        }
                        toastr.error(errorMessage);
                    }
                });
            }
        });

        // Handle image view click
        $(document).off('click', '.view-image').on('click', '.view-image', function() {
            const imageUrl = $(this).data('url');
            const filename = $(this).data('filename');

            // Create simple lightbox overlay
            const overlay = `
            <div id="image-overlay">
                <div class="image-overlay-content">
                    <div class="image-overlay-header">
                        <span class="image-filename">${filename}</span>
                        <a href="javascript:void(0)" class="close-overlay">&times;</a>
                    </div>
                    <div class="image-container">
                        <img src="${imageUrl}" alt="${filename}">
                    </div>
                    <div class="image-overlay-footer">
                        <a href="${imageUrl}" class="btn btn-info btn-sm" download>Download</a>
                    </div>
                </div>
            </div>
            `;

            // Remove any existing overlay
            $('#image-overlay').remove();

            // Add overlay to body
            $('body').append(overlay);

            // Show overlay with fade effect
            $('#image-overlay').fadeIn(200);

            // Handle close button click
            $('.close-overlay').on('click', function() {
                $('#image-overlay').fadeOut(200, function() {
                    $(this).remove();
                });
            });

            // Close overlay when clicking outside the image
            $('#image-overlay').on('click', function(e) {
                if ($(e.target).is('#image-overlay')) {
                    $('#image-overlay').fadeOut(200, function() {
                        $(this).remove();
                    });
                }
            });

            // Prevent scrolling of the body when overlay is open
            $('body').css('overflow', 'hidden');

            // Restore scrolling when overlay is closed
            $('#image-overlay').on('remove', function() {
                $('body').css('overflow', '');
            });
        });
    });
</script>
<?php /**PATH C:\Content\Laravel\CRM-Grow\application\resources\views/pages/task/components/tester-content.blade.php ENDPATH**/ ?>