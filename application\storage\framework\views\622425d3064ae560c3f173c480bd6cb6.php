<?php if(auth()->user()->is_master): ?>
    <!-- Master user phase selection code remains the same -->
    <div class="form-group row">
        <label class="col-sm-12 col-lg-3 text-left control-label col-form-label required">Phases</label>
        <div class="col-sm-12 col-lg-9">
            <select name="phase_id" id="phase_id" class="select2-basic-search form-control form-control-sm">
                <option value="">Select a Phase</option>
                <?php $__currentLoopData = $phases; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $phase): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e($phase->id); ?>"><?php echo e($phase->name); ?></option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </select>
        </div>
    </div>
<?php endif; ?>

<!-- Common table for both master and non-master users -->
<div class="table-responsive d-none" id="phase-details-table">
    <table class="table table-bordered" id="phases-table">
        <thead>
            <tr>
                <th>Phase</th>
                <th>Start Date</th>
                <th>End Date</th>
                <th>Users</th>
                <th>Status</th>
                <?php if(auth()->user()->is_master): ?>
                    <th>Action</th>
                <?php endif; ?>
            </tr>
        </thead>
        <tbody id="phase-details-body">
            <!-- Will be populated via JavaScript -->
        </tbody>
    </table>
</div>

<!-- Active Phase Selection - For both master and non-master users -->
<div class="form-group row d-none" id="active-phase-container">
    <label class="col-sm-12 col-lg-3 text-left control-label col-form-label required">Active Phase</label>
    <div class="col-sm-12 col-lg-9">
        <select name="active_phase_id" id="active_phase_id" class="select2-basic form-control form-control-sm" required>
            <option value="">Select Active Phase</option>
        </select>
    </div>
</div>


<?php
    $phaseStatuses = \App\Models\PhaseStatus::where('is_active', true)->orderBy('position', 'asc')->get();
    //I don't want to show the Upcoming task  in status
$phaseStatuses = $phaseStatuses
    ->filter(function ($item, $key) {
        return $item->phasestatus_title != 'Upcoming';
        })
        ->values(); // Add ->values() to reindex the array

?>



<script>
    $(document).ready(function() {
        var selectedProject = null;
        var start_date_due = null;
        var end_date_due = null;

        $('#task_projectid').on('change', function() {
            selectedProject = $(this).val();
        });
        $('#task_date_duee').on('change', function() {
            end_date_due = $(this).val();

        });


        $('#start_date_due').on('change', function() {
            start_date_due = $(this).val();
        });
        //when user click in the select phase we will only allow him to select phases if both start and end date are selected
        $('#phase_id').prop('disabled', true);

        // Check dates when they change and enable/disable phase dropdown
        $('#start_date_due, #task_date_duee').on('change', function() {
            const startDate = $('#start_date_due').val();
            const endDate = $('#task_date_duee').val();

            // end date start date vanda agadi ko huna vayena..
            if (startDate && endDate && endDate < startDate) {
                toastr.error('End date should be greater than or equal to start date');
                $(this).val(''); // Clear the invalid date
                return;
            }

            if (startDate && endDate) {
                $('#phase_id').prop('disabled', false);
            } else {
                $('#phase_id').prop('disabled', true);
            }
        });



        // Define phaseStatuses at the top of your script
        var phaseStatuses = <?php echo json_encode($phaseStatuses, 15, 512) ?>;

        // Add this line to check if phaseStatuses is loaded correctly
        console.log('Phase statuses loaded:', phaseStatuses);
        // Initialize select2
        $('.select2-basic-search, .select2-basic').select2();

        const isMaster = <?php echo e(auth()->user()->is_master ? 'true' : 'false'); ?>;

        if (!isMaster) {
            // For non-master users, automatically fetch their phases
            fetchUserPhases();
        }

        // Call syncPhaseUsersWithAssigned whenever there's a change in phase users
        $(document).on('change', 'select[name^="phases"][name$="[users][]"]', function() {
            syncPhaseUsersWithAssigned();
        });

        // Also call it when the document is ready to sync initial values
        setTimeout(syncPhaseUsersWithAssigned, 500);

        function fetchUserPhases() {
            $.ajax({
                url: '/user/phases',
                method: 'GET',
                success: function(response) {
                    // Check if phases exist and have length
                    if (!response.phases || response.phases.length === 0) {
                        // Hide table and active phase container
                        $('#phase-details-table').addClass('d-none');
                        $('#active-phase-container').addClass('d-none');
                        return;
                    }

                    // Show table and active phase container only if phases exist
                    $('#phase-details-table').removeClass('d-none');
                    $('#active-phase-container').removeClass('d-none');

                    // Get current user info from response
                    const currentUser = {
                        id: Object.keys(response.current_user)[0],
                        name: Object.values(response.current_user)[0]
                    };

                    response.phases.forEach(phase => {
                        addPhaseToTable(phase.id, phase.name, currentUser);

                        // Add to active phase dropdown
                        $('#active_phase_id').append(new Option(phase.name, phase.id));
                    });

                    // For non-master, automatically set the first phase as active
                    if (response.phases.length > 0) {
                        const firstPhase = response.phases[0];
                        $('#active_phase_id').val(firstPhase.id).trigger('change');

                        // Call syncPhaseUsersWithAssigned after phases are loaded
                        setTimeout(syncPhaseUsersWithAssigned, 300);
                    }
                },
                error: function(xhr) {
                    // Hide table and active phase container on error
                    $('#phase-details-table').addClass('d-none');
                    $('#active-phase-container').addClass('d-none');
                    console.error('Error fetching user phases');
                    toastr.error('Error fetching user phases');
                }
            });
        }

        function addPhaseToTable(phaseId, phaseName, currentUser) {
            // Check if phaseStatuses is available
            if (!phaseStatuses || phaseStatuses.length === 0) {
                console.error('Phase statuses not available');
                toastr.error('Phase statuses not available');
                return; // Exit if no statuses available
            }

            let html = `
        <tr data-phase-id="${phaseId}">
            <td>${phaseName}
                <input type="hidden" name="phases[${phaseId}][id]" value="${phaseId}">
                <input type="hidden" name="phases[${phaseId}][name]" value="${phaseName}">
            </td>
            <td>
                <input type="date" name="phases[${phaseId}][start_date]" class="form-control form-control-sm">
            </td>
            <td>
                <input type="date" name="phases[${phaseId}][end_date]" class="form-control form-control-sm">
            </td>
            <td>`;

            if (isMaster) {
                html += `
                <select name="phases[${phaseId}][users][]" class="select2-basic form-control form-control-sm" multiple>
                    ${users.map(user => `
                        <option value="${user.id}">${user.first_name}</option>
                    `).join('')}
                </select>`;
            } else {
                // For non-master, show only the current user
                html += `
                <select name="phases[${phaseId}][users][]" class="select2-basic form-control form-control-sm" disabled>
                    <option value="${currentUser.id}" selected>${currentUser.name}</option>
                </select>
                <input type="hidden" name="phases[${phaseId}][users][]" value="${currentUser.id}">`;
            }

            html += `</td>
            <td>
                <select name="phases[${phaseId}][status_id]" class="select2-basic form-control form-control-sm phase-status-select">
                    <option value="">Select Status</option>`;

            // Log each status as we add it to verify
            phaseStatuses.forEach(status => {
                console.log('Adding status to dropdown:', status);
                html += `<option value="${status.id}">${status.phasestatus_title}</option>`;
            });

            html += `</select>
            </td>`;

            if (isMaster) {
                html += `
                <td>
                    <button type="button" class="btn btn-danger btn-sm remove-phase">
                        <i class="sl-icon-trash"></i>
                    </button>
                </td>`;
            }

            html += `</tr>`;

            $('#phase-details-body').append(html);

            // Force select2 initialization immediately
            $(`tr[data-phase-id="${phaseId}"] select.phase-status-select`).select2({
                dropdownParent: $('#phases-table').parent()
            });

            // Initialize other select2 elements
            $(`tr[data-phase-id="${phaseId}"] select.select2-basic`).not('.phase-status-select').select2({
                dropdownParent: $('#phases-table').parent()
            }).on('change', function() {
                syncPhaseUsersWithAssigned();
            });

            console.log('Select2 initialization complete for phase:', phaseId);
        }

        // Handle active phase change for both master and non-master users
        $('#active_phase_id').on('change', function() {
            const selectedPhaseId = $(this).val();
            if (selectedPhaseId) {
                // You can add any additional logic here when active phase changes
                console.log('Active phase changed to:', selectedPhaseId);
            }
        });

        // Existing master user code for phase selection
        if (isMaster) {
            $('#phase_id').on('change', function() {
                var phaseId = $(this).val();
                var phaseName = $(this).find('option:selected').text();

                if (!phaseId) return;

                // Check if this phase is already added
                if ($(`tr[data-phase-id="${phaseId}"]`).length > 0) {
                    toastr.error('This phase has already been added');
                    $(this).val('').trigger('change');
                    return;
                }

                // Fetch phase details including users
                $.ajax({
                    url: '/phases/' + phaseId + '/details' + '?project=' + selectedProject,
                    method: 'GET',
                    success: function(response) {
                        // Show the table
                        $('#phase-details-table').removeClass('d-none');
                        $('#active-phase-container').removeClass('d-none');

                        // Create the table row
                        var html = `
                        <tr data-phase-id="${phaseId}">
    <td>${phaseName}
        <input type="hidden" name="phases[${phaseId}][id]" value="${phaseId}">
        <input type="hidden" name="phases[${phaseId}][name]" value="${phaseName}">
    </td>
    <td>
        <input type="date" name="phases[${phaseId}][start_date]" class="form-control form-control-sm">
    </td>
    <td>
        <input type="date" name="phases[${phaseId}][end_date]" class="form-control form-control-sm">
    </td>
    <td>
        <select name="phases[${phaseId}][users][]" class="select2-basic form-control form-control-sm" multiple>
            ${response.users.map(user => `
                <option value="${user.id}">${user.first_name}</option>
            `).join('')}
        </select>
    </td>
    <td>
        <select name="phases[${phaseId}][status_id]" class="select2-basic form-control form-control-sm phase-status-select">
            <option value="">Select Status</option>
            ${phaseStatuses.map(status => `
                <option value="${status.id}">${status.phasestatus_title}</option>
            `).join('')}
        </select>
    </td>
    <td>
        <button type="button" class="btn btn-danger btn-sm remove-phase">
            <i class="sl-icon-trash"></i>
        </button>
    </td>
                        </tr>
                        `;
                        // Append the new row
                        $('#phase-details-body').append(html);

                        // Add option to active phase dropdown
                        $('#active_phase_id').append(new Option(phaseName, phaseId));

                        // Reset the select
                        $('#phase_id').val('').trigger('change');

                        // Reinitialize select2 for the new dropdown
                        $(`tr[data-phase-id="${phaseId}"] .select2-basic`).select2().on(
                            'change',
                            function() {
                                // If this is a user selection dropdown, sync with assigned users
                                if ($(this).attr('name').includes('[users]')) {
                                    syncPhaseUsersWithAssigned();
                                }
                            });

                        // Call syncPhaseUsersWithAssigned after adding the phase
                        setTimeout(syncPhaseUsersWithAssigned, 300);
                    },
                    error: function(xhr) {
                        console.error('Error fetching phase details');
                        toastr.error('Error fetching phase details');
                    }
                });
            });

            // Remove phase row
            $(document).on('click', '.remove-phase', function() {
                var row = $(this).closest('tr');
                var phaseId = row.data('phase-id');

                // Remove the option from active phase dropdown
                $(`#active_phase_id option[value="${phaseId}"]`).remove();
                $('#active_phase_id').trigger('change');

                // Remove the row
                row.remove();

                // Hide table and active phase selector if no phases are selected
                if ($('#phase-details-body tr').length === 0) {
                    $('#phase-details-table').addClass('d-none');
                    $('#active-phase-container').addClass('d-none');
                }
            });
        }

        // Add this function to sync phase users with assigned users
        function syncPhaseUsersWithAssigned() {
            // Get all selected users from phase rows
            let allPhaseUsers = [];
            $('#phase-details-body tr').each(function() {
                const phaseId = $(this).data('phase-id');
                if (phaseId) {
                    // Get selected users for this phase
                    const userSelect = $(this).find('select[name^="phases"][name$="[users][]"]');
                    const selectedUsers = userSelect.val() || [];

                    // Add to our collection
                    allPhaseUsers = [...allPhaseUsers, ...selectedUsers];
                }
            });

            // Remove duplicates
            allPhaseUsers = [...new Set(allPhaseUsers)];

            // Get current assigned users
            const currentAssigned = $('#assigned').val() || [];

            // Merge arrays without duplicates
            const newAssigned = [...new Set([...currentAssigned, ...allPhaseUsers])];

            // Update the assigned users select
            const assignedSelect = $('#assigned');

            // For each user in allPhaseUsers, check if they exist in the select
            allPhaseUsers.forEach(userId => {
                // If the option doesn't exist in the select, we need to add it
                if (!assignedSelect.find(`option[value="${userId}"]`).length) {
                    // Find the user name from the phase user select
                    let userName = '';
                    $('#phase-details-body tr').each(function() {
                        const userSelect = $(this).find(
                            'select[name^="phases"][name$="[users][]"]');
                        const option = userSelect.find(`option[value="${userId}"]:selected`);
                        if (option.length) {
                            userName = option.text();
                            return false; // Break the loop once found
                        }
                    });

                    // Add the new option
                    if (userName) {
                        const newOption = new Option(userName, userId, true, true);
                        assignedSelect.append(newOption);
                    }
                }
            });

            // Update the select2 with new values
            assignedSelect.val(newAssigned).trigger('change');

            console.log('Synced phase users with assigned users:', newAssigned);
        }

        // Call this function when phase users change
        $(document).on('change', 'select[name^="phases"][name$="[users][]"]', function() {
            syncPhaseUsersWithAssigned();
        });

        // Add date validation for phase dates
        $(document).on('change', 'input[name*="[start_date]"], input[name*="[end_date]"]', function() {
            const mainStartDate = $('#start_date_due').val();
            const mainEndDate = $('#task_date_duee').val();

            if (!mainStartDate || !mainEndDate) {
                toastr.error('Please select main task dates first');
                $(this).val('');
                return;
            }

            const phaseDate = $(this).val();
            const isStartDate = $(this).attr('name').includes('[start_date]');

            if (phaseDate) {
                if (phaseDate < mainStartDate) {
                    toastr.error(
                        `Phase ${isStartDate ? 'start' : 'end'} date cannot be before main task start date`
                    );
                    $(this).val('');
                } else if (phaseDate > mainEndDate) {
                    toastr.error(
                        `Phase ${isStartDate ? 'start' : 'end'} date cannot be after main task end date`
                    );
                    $(this).val('');
                }
            }
        });
    });
</script>
<?php /**PATH C:\Content\Laravel\CRM-Grow\application\resources\views/pages/tasks/components/modals/task-phases.blade.php ENDPATH**/ ?>