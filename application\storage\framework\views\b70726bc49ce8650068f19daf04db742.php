<?php $__currentLoopData = $board['tasks']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $subtask): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <?php
        // Calculate subtask progress if needed
        $subtask_progress = 0; // You can implement checklist progress calculation here
    ?>
    
    <!--each subtask card-->
    <div class="kanban-card kanban-board-element show-modal-button reset-card-modal-form js-ajax-ux-request <?php echo e($subtask->pinned_status ?? ''); ?>"
        data-toggle="modal" data-target="#commonModal"
        data-url="<?php echo e(url('/subtasks/' . $subtask->id . '/details')); ?>"
        data-subtask-id="<?php echo e($subtask->id); ?>" data-task-id="<?php echo e($subtask->task_id); ?>" 
        data-loading-target="main-top-nav-bar"
        id="card_subtask_<?php echo e($subtask->id); ?>">

        <div class="kanban-card-content-comntainer">
            <div class="x-title wordwrap" id="kanban_subtask_title_<?php echo e($subtask->id); ?>">
                <?php echo e($subtask->subtask_title); ?>

                <span class="x-action-button" id="subtask-action-button-<?php echo e($subtask->id); ?>" data-toggle="dropdown"
                    aria-haspopup="true" aria-expanded="false"><i class="mdi mdi-dots-vertical"></i></span>
                <div class="dropdown-menu dropdown-menu-small dropdown-menu-right js-stop-propagation"
                    aria-labelledby="subtask-action-button-<?php echo e($subtask->id); ?>">
                    
                    <!--edit subtask-->
                    <a class="dropdown-item edit-add-modal-button js-ajax-ux-request reset-target-modal-form"
                        data-toggle="modal" data-target="#commonModal" data-modal-title="Edit Subtask"
                        data-url="<?php echo e(url('/subtasks/' . $subtask->id . '/edit')); ?>"
                        data-action-url="<?php echo e(url('/subtasks/' . $subtask->id . '/update')); ?>"
                        data-modal-size="modal-lg" data-loading-target="commonModalBody"
                        data-action-method="PUT" aria-expanded="false">
                        Edit Subtask
                    </a>

                    <!--delete subtask-->
                    <a class="dropdown-item confirm-action-danger js-stop-propagation"
                        data-confirm-title="Delete Subtask"
                        data-confirm-text="Are you sure you want to delete this subtask?" 
                        data-ajax-type="DELETE"
                        data-url="<?php echo e(url('/subtasks/' . $subtask->id)); ?>">
                        Delete
                    </a>

                    <!--view parent task-->
                    <a class="dropdown-item" 
                        href="<?php echo e(url('/tasks/' . $subtask->task_id)); ?>">
                        View Parent Task
                    </a>
                </div>
            </div>

            <!--priority badge below title-->
            <div class="subtask-priority-section mb-2">
                <span class="badge badge-sm badge-info">
                    <i class="fas fa-tasks"></i> Subtask
                </span>
            </div>

            <div class="x-meta">
                <!--parent task-->
                <span title="<?php echo e($subtask->task_title); ?>">
                    <strong>Task:</strong> <?php echo e(str_limit($subtask->task_title, 30)); ?>

                </span>

                <!--project-->
                <span title="<?php echo e($subtask->project_title); ?>">
                    <strong>Project:</strong> <?php echo e(str_limit($subtask->project_title, 25)); ?>

                </span>

                <!--status-->
                <div class="mt-2">
                    <label class="label label-<?php echo e($subtask->taskstatus_color); ?> p-t-3 p-b-3 p-l-8 p-r-8"
                        data-toggle="tooltip" title="Status">
                        <?php echo e($subtask->taskstatus_title); ?>

                    </label>
                </div>

                <!--start date-->
                <span>
                    <strong>Start:</strong> <?php echo e(\Carbon\Carbon::parse($subtask->subtask_start_date)->format('M d, Y')); ?>

                </span>

                <!--end date-->
                <span>
                    <strong>Due:</strong> <?php echo e(\Carbon\Carbon::parse($subtask->subtask_end_date)->format('M d, Y')); ?>

                </span>

                <!--progress (if you implement checklist progress)-->
                <?php if($subtask_progress > 0): ?>
                    <span>
                        <strong>Progress:</strong> <?php echo e($subtask_progress); ?>%
                    </span>
                <?php endif; ?>
            </div>

            <div class="x-footer row">
                <div class="col-6 x-icons">
                    <!--subtask icon-->
                    <span class="x-icon text-info display-inline-block vm p-t-2" 
                        data-toggle="tooltip" title="Subtask" data-placement="top">
                        <i class="fas fa-tasks"></i>
                    </span>

                    <!--checklist icon (if has checklists)-->
                    <?php if($subtask->checklist_id): ?>
                        <span class="x-icon display-inline-block vm">
                            <i class="fas fa-check-square"></i>
                        </span>
                    <?php endif; ?>

                    <!--overdue indicator-->
                    <?php if(\Carbon\Carbon::parse($subtask->subtask_end_date)->isPast() && $subtask->subtask_status_id != 2): ?>
                        <span class="x-icon text-danger display-inline-block vm p-t-2" 
                            data-toggle="tooltip" title="Overdue" data-placement="top">
                            <i class="fas fa-exclamation-triangle"></i>
                        </span>
                    <?php endif; ?>
                </div>

                <div class="col-6 x-assigned">
                    <!--assigned user avatar-->
                    <?php if($subtask->assigned_first_name): ?>
                        <div class="assigned-user-avatar" data-toggle="tooltip" 
                            title="<?php echo e($subtask->assigned_first_name); ?> <?php echo e($subtask->assigned_last_name); ?>">
                            <?php if(isset($subtask->assigned_avatar_directory) && isset($subtask->assigned_avatar_filename)): ?>
                                <img src="<?php echo e(getUsersAvatar($subtask->assigned_avatar_directory, $subtask->assigned_avatar_filename)); ?>" 
                                    class="img-circle avatar-xsmall" 
                                    alt="<?php echo e($subtask->assigned_first_name); ?>">
                            <?php else: ?>
                                <div class="avatar-fallback">
                                    <?php echo e(substr($subtask->assigned_first_name, 0, 1)); ?><?php echo e(substr($subtask->assigned_last_name ?? '', 0, 1)); ?>

                                </div>
                            <?php endif; ?>
                        </div>
                    <?php else: ?>
                        <span class="badge badge-light-secondary">
                            <i class="fas fa-user-slash"></i>
                            Unassigned
                        </span>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

<style>
.subtask-priority-section {
    margin-bottom: 8px;
}

.subtask-priority-section .badge {
    font-size: 10px;
    font-weight: 500;
}

.assigned-user-avatar {
    display: inline-block;
    float: right;
}

.avatar-fallback {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
    border: 2px solid #fff;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}
</style>
<?php /**PATH C:\Content\Laravel\CRM-Grow\application\resources\views/pages/tasks/components/kanban/subtask_card.blade.php ENDPATH**/ ?>